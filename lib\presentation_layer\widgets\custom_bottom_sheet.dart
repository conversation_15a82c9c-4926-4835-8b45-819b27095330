import 'package:flutter/material.dart';

class CustomBottomSheet extends StatelessWidget {
  final Widget child;
  final Color? backgroundColor;
  final double? borderRadius;
  final double? boxShadowBlurRadius;
  final double? boxShadowSpreadRadius;
  final Color? boxShadowColor;

  const CustomBottomSheet({
    Key? key,
    required this.child,
    this.backgroundColor,
    this.borderRadius,
    this.boxShadowBlurRadius,
    this.boxShadowSpreadRadius,
    this.boxShadowColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.transparent,
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(
          decoration: BoxDecoration(
            color: backgroundColor ?? Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(borderRadius ?? 0),
              topRight: Radius.circular(borderRadius ?? 0),
            ),
            boxShadow: [
              BoxShadow(
                color: boxShadowColor ?? Colors.grey.withOpacity(0.3),
                blurRadius: boxShadowBlurRadius ?? 10.0,
                spreadRadius: boxShadowSpreadRadius ?? 2.0,
              ),
            ],
          ),
          child: child,
        ),
      ),
    );
  }
}
