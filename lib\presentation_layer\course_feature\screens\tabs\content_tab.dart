import 'package:arepsalin/data_layer/model/week_subject_model.dart';
import 'package:flutter/material.dart';
import 'package:arepsalin/presentation_layer/course_feature/widgets/general_item.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../../constants/app_colors.dart';
import '../../../../managers/theme_manager.dart';
import '../../../../translation/locale_keys.g.dart';
import '../pdf_screen.dart';

class ContentTab extends StatefulWidget {
  const ContentTab({
    super.key,
    required this.semesterNo,
    required this.weekNo,
    required this.subject,
  });

  final int weekNo;
  final int semesterNo;
  final WeekSubject subject;

  @override
  State<ContentTab> createState() => _ContentTabState();
}

class _ContentTabState extends State<ContentTab> {
  @override
  Widget build(BuildContext context) {
    // Filter items where itemType is pdf from all lessons
    final pdfItems = widget.subject.lessons
        .expand((lesson) => lesson.items)
        .where((item) => item.itemType == "pdf")
        .toList();

    return Column(
      children: [
        if (pdfItems.isEmpty)
          Center(
            child: Text(
              LocaleKeys.noContentAvailable.tr(),
              style: ThemeManager.medium(
                size: 16,
                color: AppColors.black.withOpacity(0.7),
              ),
            ),
          )
        else
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColors.warmWhite,
              border: Border.all(
                color: AppColors.grey.withOpacity(0.5),
                width: 1,
              ),
            ),
            child: ListView.separated(
              shrinkWrap: true,
              itemBuilder: (context, index) => GeneralItem(
                onTabBackFunction: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => PdfScreen(
                        title: pdfItems[index].title,
                        pdf: pdfItems[index].itemContent,
                      ),
                    ),
                  );
                },
                title: '${index + 1} - ${pdfItems[index].title}',
                icon: Icons.file_copy_outlined,
              ),
              separatorBuilder: (context, index) => Container(
                color: AppColors.primary,
                height: 1,
                width: double.infinity,
              ),
              itemCount: pdfItems.length,
            ),
          ),
      ],
    );
  }
}
