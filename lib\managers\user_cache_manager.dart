import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter_cache_manager/flutter_cache_manager.dart';

import '../data_layer/model/user_model.dart';

class UserCacheManager {
  static const key = "userCacheKey";

  static CacheManager instance = CacheManager(
    Config(
      key,
      stalePeriod: const Duration(days: 1500),
      maxNrOfCacheObjects: 20,
    ),
  );

  static Future<void> cacheUserData(Map<String, dynamic> data) async {
    String jsonData = jsonEncode(data); // Convert to valid JSON string
    Uint8List uint8ListData = Uint8List.fromList(jsonData.codeUnits);
    await instance.putFile(key, uint8ListData);
  }

  static Future<User?> getCachedUserData() async {
    final fileInfo = await instance.getFileFromCache(key);
    if (fileInfo != null) {
      String jsonString = String.fromCharCodes(fileInfo.file.readAsBytesSync());
      Map<String, dynamic> userMap =
          jsonDecode(jsonString); // Decode the JSON string into a map
      return User.fromJson(
          userMap); // Convert the map to a UserModel instance
    }
    return null;
  }

  static Future<void> clearCache() async {
    await instance.emptyCache();
  }
}
