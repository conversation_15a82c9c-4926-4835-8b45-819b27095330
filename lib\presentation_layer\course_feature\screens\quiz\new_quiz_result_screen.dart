import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/constants/app_images.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../widgets/custom_button.dart';

class NewQuizResultScreen extends StatelessWidget {
  const NewQuizResultScreen({
    super.key,
    required this.score,
    required this.totalScore,
    required this.quizName,
    this.isFinal = false,
  });

  final int score;
  final int totalScore;
  final String quizName;
  final bool isFinal;

  @override
  Widget build(BuildContext context) {
    final double percentage = (score / totalScore) * 100;
    final bool isPassed = percentage >= 50;
    
    // Determine status
    String status = 'pending';
    if (percentage == 100) {
      status = 'excellent';
    } else if (percentage >= 50) {
      status = 'pass';
    } else {
      status = 'fail';
    }
    
    // Get color based on status
    Color statusColor;
    switch (status) {
      case 'pending':
        statusColor = const Color(0xFFFFA500); // Orange/Amber
        break;
      case 'pass':
        statusColor = const Color(0xFF4CAF50); // Green
        break;
      case 'fail':
        statusColor = const Color(0xFFF44336); // Red
        break;
      case 'excellent':
        statusColor = const Color(0xFFFFD700); // Gold
        break;
      default:
        statusColor = isFinal ? AppColors.bermuda : AppColors.primary;
    }

    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: statusColor,
      statusBarIconBrightness: Brightness.dark,
    ));

    return Scaffold(
      appBar: AppBar(
        foregroundColor: AppColors.warmWhite,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        title: Text(
          quizName,
          style: ThemeManager.medium(color: AppColors.white, size: 22),
        ),
        centerTitle: true,
        backgroundColor: statusColor,
        automaticallyImplyLeading: false,
        elevation: 0,
      ),
      body: Stack(
        children: [
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Image(
              image: AssetImage(AppImages.backgroundImage),
              fit: BoxFit.fill,
            ),
          ),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        isPassed ? Icons.check_circle : Icons.cancel,
                        size: 80,
                        color: isPassed ? Colors.green : Colors.red,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        isPassed
                            ? LocaleKeys.congratulationsYouPassedInThisExam.tr()
                            : LocaleKeys.sorryYouFailed.tr(),
                        style: ThemeManager.bold(
                          size: 24,
                          color: isPassed ? Colors.green : Colors.red,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 30),
                      _buildScoreCircle(percentage),
                      const SizedBox(height: 20),
                      Column(
                        children: [
                          Text(
                            '$score / $totalScore ${LocaleKeys.points.tr()}',
                            style: ThemeManager.bold(
                              size: 20,
                              color: AppColors.black,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${percentage.toInt()}% ${LocaleKeys.correct.tr()}',
                            style: ThemeManager.medium(
                              size: 16,
                              color: isPassed ? Colors.green : Colors.red,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 30),
                      CustomButton(
                        height: 56,
                        width: double.infinity,
                        title: LocaleKeys.ok.tr(),
                        onPressed: () {
                          // Use PersistentNavBarNavigator to pop back to the main screen
                          Navigator.of(context)
                              .popUntil((route) => route.isFirst);
                        },
                        borderRadius: 16,
                        btnColor: statusColor,
                        textStyle: ThemeManager.semiBold(
                          size: 18,
                          color: AppColors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    status.toUpperCase(),
                    style: ThemeManager.bold(
                      size: 16,
                      color: statusColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScoreCircle(double percentage) {
    final Color progressColor = percentage >= 50 ? Colors.green : Colors.red;

    return Container(
      width: 150,
      height: 150,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 5,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: 120,
            height: 120,
            child: CircularProgressIndicator(
              value: percentage / 100,
              strokeWidth: 12,
              backgroundColor: Colors.grey.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
            ),
          ),
          Text(
            '${percentage.toInt()}%',
            style: ThemeManager.bold(
              size: 30,
              color: progressColor,
            ),
          ),
        ],
      ),
    );
  }
}
