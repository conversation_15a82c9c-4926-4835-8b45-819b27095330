import 'package:easy_localization/easy_localization.dart';
import 'package:arepsalin/data_layer/model/week_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:arepsalin/presentation_layer/course_feature/screens/lessons_screen.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_images.dart';
import '../../../data_layer/model/week_model.dart';
import '../../../data_layer/model/week_subject_model.dart';
import '../../../data_layer/service/semester_service.dart';
import '../../../managers/theme_manager.dart';
import '../../../translation/locale_keys.g.dart';
import '../../widgets/custom_progress_indicator.dart';

class SubjectScreen extends StatefulWidget {
  const SubjectScreen({
    super.key,
    required this.semesterNo,
    required this.title,
    required this.week,
    required this.subjectNames,
  });

  final String title;
  final WeekResponse week;
  final int semesterNo;
  final List<String> subjectNames;

  @override
  State<SubjectScreen> createState() => _SubjectScreenState();
}

class _SubjectScreenState extends State<SubjectScreen> {
  List<WeekSubject> weekSubjects = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    fetchWeekSubjects();
  }

  Future<void> fetchWeekSubjects() async {
    try {
      final semesterService = SemesterService();
      final subjects = await semesterService.getWeekSubjects(widget.week.id);
      
      setState(() {
        weekSubjects = subjects;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      debugPrint('Error fetching week subjects: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        centerTitle: true,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        title: Text(widget.title),
        titleTextStyle: ThemeManager.semiBold(size: 20, color: AppColors.warmWhite),
      ),
      body: Stack(
        children: [
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Image(
              image: AssetImage(AppImages.backgroundImage),
              fit: BoxFit.fill,
            ),
          ),
          Container(
            width: double.infinity,
            height: double.infinity,
            color: AppColors.white.withOpacity(0.7),
          ),
          if (isLoading)
            const Center(child: CustomProgressIndicator())
          else if (weekSubjects.isEmpty)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.school_outlined,
                    size: 64,
                    color: AppColors.primary.withOpacity(0.7),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    LocaleKeys.noSubjectsAvailable.tr(),
                    style: ThemeManager.semiBold(
                      size: 18,
                      color: AppColors.black.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            )
          else
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [

                  const SizedBox(height: 24),
                  Expanded(
                    child: GridView.builder(
                      padding: const EdgeInsets.only(bottom: 20),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 1,
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                      ),
                      itemCount: weekSubjects.length,
                      itemBuilder: (context, i) {
                        final subject = weekSubjects[i];
                        return SubjectCard(
                          subject: subject,
                          onTap: () => _navigateToWeekScreen(subject),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  void _navigateToWeekScreen(WeekSubject subject) {
    PersistentNavBarNavigator.pushNewScreen(
      context,
      screen: LessonsScreen(
        title: widget.title,
        week: widget.week,
        semesterNo: widget.semesterNo,
        subject: subject,
      ),
      withNavBar: true,
      pageTransitionAnimation: PageTransitionAnimation.cupertino,
    );
  }
}

class SubjectCard extends StatelessWidget {
  final WeekSubject subject;
  final VoidCallback onTap;

  const SubjectCard({
    Key? key,
    required this.subject,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          children: [
            Positioned(
              right: -20,
              bottom: -20,
              child: Icon(
                Icons.book_outlined,
                size: 100,
                color: AppColors.white.withOpacity(0.1),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      subject.code,
                      style: ThemeManager.medium(
                        size: 12,
                        color: AppColors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    subject.name,
                    style: ThemeManager.semiBold(
                      size: 18,
                      color: AppColors.white,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    LocaleKeys.lessonsCount.tr(args: [subject.lessons.length.toString()]),
                    style: ThemeManager.regular(
                      size: 14,
                      color: AppColors.white.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
