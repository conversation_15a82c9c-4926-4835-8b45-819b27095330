import 'package:arepsalin/data_layer/model/week_subject_model.dart';
import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../../constants/app_colors.dart';
import '../../../../translation/locale_keys.g.dart';
import '../../../../managers/theme_manager.dart';
import '../../widgets/general_item.dart';
import '../single_video_screen.dart';

class VideosTab extends StatefulWidget {
  const VideosTab({
    super.key,
    required this.semesterNo,
    required this.weekNo,
    required this.subject,
  });

  final int weekNo;
  final int semesterNo;
  final WeekSubject subject;

  @override
  State<VideosTab> createState() => _VideosTabState();
}

class _VideosTabState extends State<VideosTab> {
  @override
  Widget build(BuildContext context) {
    // Filter items where itemType is video from all lessons
    final videoItems = widget.subject.lessons
        .expand((lesson) => lesson.items)
        .where((item) => item.itemType == "video")
        .toList();

    return Column(
      children: [
        if (videoItems.isEmpty)
          Center(
            child: Text(
              LocaleKeys.noVideoAvailable.tr(),
              style: ThemeManager.medium(
                size: 16,
                color: AppColors.black.withOpacity(0.7),
              ),
            ),
          )
        else
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColors.warmWhite,
              border: Border.all(
                color: AppColors.grey.withOpacity(0.5),
                width: 1,
              ),
            ),
            child: ListView.separated(
              shrinkWrap: true,
              itemBuilder: (context, index) => GeneralItem(
                onTabBackFunction: () {
                  PersistentNavBarNavigator.pushNewScreen(
                    context,
                    screen: SingleVideoScreen(
                      title: videoItems[index].title,
                      videoUrl: videoItems[index].itemContent,
                    ),
                    withNavBar: false,
                    pageTransitionAnimation: PageTransitionAnimation.cupertino,
                  );
                },
                title: '${index + 1} - ${videoItems[index].title}',
                icon: Icons.video_library,
              ),
              separatorBuilder: (context, index) => Container(
                color: AppColors.primary,
                height: 1,
                width: double.infinity,
              ),
              itemCount: videoItems.length,
            ),
          ),
      ],
    );
  }
}
