import 'package:flutter/material.dart';
import 'package:arepsalin/data_layer/model/user_model.dart';

class AuthenticationProvider extends ChangeNotifier {
  DateTime? _selectedDate;
  bool _showPassword = false;
  User? _userModel;

  DateTime? get selectedDate => _selectedDate;

  bool get showPassword => _showPassword;

  User? get user => _userModel;

  void togglePasswordVisibility() {
    _showPassword = !_showPassword;
    notifyListeners();
  }

  void setSelectedDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
  }

  void updateUser(User user) {
    _userModel = user;
  }

  void updateUserImage(String img) {
    // _userModel?.image = img;
  }
}
