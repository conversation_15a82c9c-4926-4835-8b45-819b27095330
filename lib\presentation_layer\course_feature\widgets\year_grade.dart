import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/presentation_layer/course_feature/widgets/subject_grade.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../constants/app_colors.dart';

class YearGrade extends StatelessWidget {
  const YearGrade({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
              color: AppColors.black.withOpacity(0.5),
              blurRadius: 1,
              spreadRadius: 0.2)
        ],
        borderRadius: BorderRadius.circular(12),
        color: AppColors.darkGrey,
      ),
      child: Column(
        children: [
          Text(
            LocaleKeys.firstSemester.tr(),
            style: ThemeManager.regular(size: 16, color: AppColors.black),
          ),
          const SizedBox(
            height: 15,
          ),
          ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: 3,
            itemBuilder: (context, index) =>
                const SubjectGrade(grade: '100%', subjectName: 'تاريخ كنيسة'),
          ),
          Container(
            width: double.infinity,
            height: 2,
            color: AppColors.borderGrey,
          ),
          const SizedBox(
            height: 15,
          ),
          Text(
            LocaleKeys.secondSemester.tr(),
            style: ThemeManager.regular(size: 16, color: AppColors.black),
          ),
          const SizedBox(
            height: 15,
          ),
          ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: 3,
            itemBuilder: (context, index) =>
                const SubjectGrade(grade: '100%', subjectName: 'تاريخ كنيسة'),
          ),
          // const SizedBox(height: 15,),
        ],
      ),
    );
  }
}
