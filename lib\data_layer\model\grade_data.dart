class UserGrade {
  String name;
  String userId;
  String subject;
  int grade;
  int maxGrade;
  int year;
  int week;
  int semester;

  UserGrade({
    required this.name,
    required this.userId,
    required this.subject,
    required this.grade,
    required this.maxGrade,
    required this.year,
    required this.week,
    required this.semester,
  });

  Map<String, dynamic> toJson(String examID) {
    return {
      'Name': name,
      'UserID': userId,
      'Subject': subject,
      'Grade': grade,
      'MaxGrade': maxGrade,
      'Year': year,
      'Week': week,
      'Semester': semester,
      'ExamID': examID
    };
  }

  factory UserGrade.fromJson(Map<String, dynamic> json) {
    return UserGrade(
      name: json['Name'],
      userId: json['UserID'],
      subject: json['Subject'],
      grade: json['Grade'],
      maxGrade: json['MaxGrade'],
      year: json['Year'],
      week: json['Week'],
      semester: json['Semester'],
    );
  }
}