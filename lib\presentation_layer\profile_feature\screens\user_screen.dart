import 'package:arepsalin/data_layer/provider/user_provider.dart';
import 'package:arepsalin/managers/secure_storage.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:arepsalin/data_layer/provider/authentication_provider.dart';
import 'package:arepsalin/data_layer/provider/language_provider.dart';
import 'package:arepsalin/managers/shared_pref_manager.dart';
import 'package:arepsalin/presentation_layer/auth_feature/screens/login_screen.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';
import '../../widgets/custom_image.dart';
import '../../widgets/custom_progress_indicator.dart';
import 'grades_screen.dart';

class UserScreen extends StatefulWidget {
  const UserScreen({Key? key}) : super(key: key);

  @override
  State<UserScreen> createState() => _UserScreenState();
}

class _UserScreenState extends State<UserScreen> {
  late LanguageProvider langProvider;

  String getFirstTwoNames(String fullName) {
    List<String> names = fullName.split(" ");

    // Check if there are at least two names
    if (names.length >= 2) {
      String firstName = names[0];
      String secondName = names[1];

      return "$firstName $secondName";
    } else {
      return "-";
    }
  }

  // late UserProvider _authenticationProvider;

  Future<String?> getCachedProfileImage() async {
    // final prefs = await SharedPreferences.getInstance();
    // final user = FirebaseAuth.instance.currentUser;
    // final userId = prefs.getString('user_id') ?? '';
    // final hasDownloaded = prefs.getBool('hasImageDownload') ?? false;
    //
    // if (!hasDownloaded || userId != FirebaseAuth.instance.currentUser?.uid) {
    //   final uid = user?.uid;
    //   final storageRef =
    //       FirebaseStorage.instance.ref().child('user_images/$uid.jpg');
    //   final downloadURL = await storageRef.getDownloadURL();
    //   await prefs.setString('profile_image', downloadURL);
    //   await prefs.setBool('hasImageDownload', true);
    //   await prefs.setString(
    //       'user_id', FirebaseAuth.instance.currentUser?.uid ?? '');
    //   return (downloadURL);
    // } else {
    //   final image = prefs.getString('profile_image');
    //   return (image);
    return "https://picsum.photos/200/300";

  }

  @override
  void initState() {
    langProvider = Provider.of(context, listen: false);
    // _authenticationProvider = context.read<UserProvider>();
    super.initState();
  }

  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.sizeOf(context);
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.primary,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        centerTitle: true,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        title: Text(
          LocaleKeys.userScreen.tr(),
          style: ThemeManager.semiBold(size: 22, color: AppColors.white),
        ),
        titleTextStyle:
            ThemeManager.semiBold(size: 20, color: AppColors.warmWhite),
      ),
      body: Stack(
        children: [
          Container(
            color: AppColors.primary,
            width: size.width,
            height: size.height,
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: FractionallySizedBox(
              heightFactor: 4 / 5,
              child: Container(
                decoration: const BoxDecoration(
                    color: AppColors.warmWhite,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20))),
                // Add your content here
              ),
            ),
          ),
          Container(
            width: size.width,
            padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: size.width / 1.5,
                  clipBehavior: Clip.hardEdge,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                          color: AppColors.black.withOpacity(0.5),
                          blurRadius: 1,
                          spreadRadius: 1)
                    ],
                    color: AppColors.darkGrey,
                  ),
                  height: size.height / 3,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            border: Border.all(color: AppColors.black),
                            borderRadius: BorderRadius.circular(100),
                            boxShadow: [
                              BoxShadow(
                                  color: AppColors.black.withOpacity(0.5),
                                  blurRadius: 5,
                                  spreadRadius: 1)
                            ]),
                        clipBehavior: Clip.hardEdge,
                        child: FutureBuilder<String?>(
                            future: getCachedProfileImage(),
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                return CustomImage(
                                  addRadius: true,
                                  image: snapshot.data,
                                  fromAssets: false,
                                  radius: 100,
                                  height: size.height / 5.2,
                                  width: size.height / 5.2,
                                );
                              } else {
                                return SizedBox(
                                    height: size.height / 5.5,
                                    width: size.height / 5.5,
                                    child: const CustomProgressIndicator());
                              }
                            }),
                      ),
                      Text(
                        getFirstTwoNames("_authenticationProvider.user!.email"),
                        style: ThemeManager.semiBold(
                            size: 22, color: AppColors.black),
                      )
                    ],
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Column(
                          children: [
                            const SizedBox(
                              height: 30,
                            ),
                            InkWell(
                              onTap: () {
                                PersistentNavBarNavigator.pushNewScreen(
                                  context,
                                  screen: const GradesScreen(),
                                  withNavBar: true,
                                  pageTransitionAnimation:
                                      PageTransitionAnimation.cupertino,
                                );
                              },
                              child: Container(
                                //margin: const EdgeInsets.symmetric(vertical: 30),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 11),
                                //height: size.height / 12.5,
                                decoration: BoxDecoration(
                                  boxShadow: [
                                    BoxShadow(
                                        color: AppColors.black.withOpacity(0.5),
                                        blurRadius: 1,
                                        spreadRadius: 0.2)
                                  ],
                                  borderRadius: BorderRadius.circular(12),
                                  color: AppColors.darkGrey,
                                ),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        const Icon(Icons.credit_score_outlined),
                                        const SizedBox(
                                          width: 5,
                                        ),
                                        Text(
                                          LocaleKeys.grades.tr(),
                                          style: ThemeManager.semiBold(
                                              size: 18, color: AppColors.black),
                                        ),
                                        const Spacer(),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                            InkWell(
                              onTap: () {
                                setState(() {
                                  isExpanded = !isExpanded;
                                });
                              },
                              child: Container(
                                //margin: const EdgeInsets.symmetric(vertical: 30),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 11),
                                //height: size.height / 12.5,
                                decoration: BoxDecoration(
                                  boxShadow: [
                                    BoxShadow(
                                        color: AppColors.black.withOpacity(0.5),
                                        blurRadius: 1,
                                        spreadRadius: 0.2)
                                  ],
                                  borderRadius: BorderRadius.circular(12),
                                  color: AppColors.darkGrey,
                                ),
                                child: Consumer<LanguageProvider>(
                                  builder: (context, value, child) {
                                    langProvider = value;
                                    langProvider.loadLanguage();
                                    return Column(
                                      children: [
                                        Row(
                                          children: [
                                            const Icon(Icons.language_outlined),
                                            const SizedBox(
                                              width: 5,
                                            ),
                                            Text(
                                              LocaleKeys.lang.tr(),
                                              style: ThemeManager.semiBold(
                                                  size: 18,
                                                  color: AppColors.black),
                                            ),
                                            const Spacer(),
                                            Icon(
                                              !isExpanded
                                                  ? Icons.arrow_drop_down
                                                  : Icons.arrow_drop_up,
                                              size: 26,
                                            )
                                          ],
                                        ),
                                        !isExpanded
                                            ? Container()
                                            : Padding(
                                                padding: const EdgeInsets.only(
                                                    top: 11.0),
                                                child: Column(
                                                  children: [
                                                    Container(
                                                      width: double.infinity,
                                                      height: 1,
                                                      color: AppColors
                                                          .actionsColor
                                                          .withOpacity(0.5),
                                                    ),
                                                    const SizedBox(
                                                      height: 10,
                                                    ),
                                                    InkWell(
                                                      onTap: () {
                                                        langProvider
                                                            .setLanguage(
                                                                context, 'ar');
                                                      },
                                                      child: Row(
                                                        children: [
                                                          Text(
                                                            'اللغة العربية',
                                                            style: ThemeManager.medium(
                                                                size: 18,
                                                                color: langProvider
                                                                            .currentLanguage ==
                                                                        'ar'
                                                                    ? AppColors
                                                                        .primary
                                                                    : AppColors
                                                                        .grey),
                                                          ),
                                                          const Spacer(),
                                                          langProvider.currentLanguage ==
                                                                  'ar'
                                                              ? const Icon(
                                                                  Icons.check,
                                                                  color: AppColors
                                                                      .primary,
                                                                )
                                                              : Container()
                                                        ],
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      height: 10,
                                                    ),
                                                    InkWell(
                                                      onTap: () {
                                                        langProvider
                                                            .setLanguage(
                                                                context, 'en');
                                                      },
                                                      child: Row(
                                                        children: [
                                                          Text(
                                                            'English',
                                                            style: ThemeManager.medium(
                                                                size: 18,
                                                                color: langProvider
                                                                            .currentLanguage ==
                                                                        'en'
                                                                    ? AppColors
                                                                        .primary
                                                                    : AppColors
                                                                        .grey),
                                                          ),
                                                          const Spacer(),
                                                          langProvider.currentLanguage ==
                                                                  'en'
                                                              ? const Icon(
                                                                  Icons.check,
                                                                  color: AppColors
                                                                      .primary,
                                                                )
                                                              : Container()
                                                        ],
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      height: 0,
                                                    ),
                                                  ],
                                                ),
                                              )
                                      ],
                                    );
                                  },
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 30,
                            ),
                            InkWell(
                                onTap: () {
                                  PersistentNavBarNavigator.pushNewScreen(
                                    context,
                                    screen: const LoginScreen(),
                                    withNavBar: false,
                                    pageTransitionAnimation:
                                        PageTransitionAnimation.cupertino,
                                  );
                                  SecureStorage s = SecureStorage();
                                  s.deleteToken("refresh_token");
                                  s.deleteToken("access_token");
                                },
                                child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 12),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      color: AppColors.primary,
                                    ),
                                    width: double.infinity,
                                    child: Center(
                                      child: Text(
                                        LocaleKeys.logOut.tr(),
                                        style: ThemeManager.medium(
                                          size: 16,
                                          color: AppColors.white,
                                        ),
                                      ),
                                    ))),
                          ],
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
