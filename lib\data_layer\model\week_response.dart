class WeekResponse {
  final int id;
  final int weekNo;
  final DateTime startDate;
  final DateTime endDate;

  WeekResponse({
    required this.id,
    required this.weekNo,
    required this.startDate,
    required this.endDate,
  });

  factory WeekResponse.fromJson(Map<String, dynamic> json) {
    return WeekResponse(
      id: json['id'],
      weekNo: json['weekNo'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
    );
  }
}
