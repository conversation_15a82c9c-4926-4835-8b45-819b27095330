import 'dart:developer';

import 'package:flutter/material.dart';

import '../../constants/app_colors.dart';
import '../../managers/theme_manager.dart';

class CustomTextFormField extends StatelessWidget {
  final String? label;
  final String? hint;
  final Color? hintColor;
  final double? width;
  final double? height;
  final bool enabled;
  final bool obscureText;
  final bool addHintVerticalPadding;
  final Widget? suffix;
  final Widget? prefix;
  final TextEditingController? controller;
  final Function()? onChange;
  final Function(String)? onFieldSubmitted;
  final TextInputType? keyboardType;
  final Color? color;
  final int maxLines;

  //final FocusNode? focusNode;
  final dynamic Function(String?)? validator;
  final bool withBorder;
  final int errorMaxLines;
  final bool withFocusedBorder;
  final bool textWithHorizontalPadding;
  final double? leftPadding;

  CustomTextFormField(
      {Key? key,
      this.controller,
      this.hint,
      this.label,
      this.suffix,
      this.prefix,
      this.onChange,
      this.onFieldSubmitted,
      this.enabled = true,
      this.obscureText = false,
      this.addHintVerticalPadding = false,
      this.keyboardType,
      this.width,
      this.height,
      this.leftPadding = 12,
      this.color = AppColors.primary,
      this.maxLines = 1,
      //this.focusNode,
      this.validator,
      this.withBorder = false,
      this.errorMaxLines = 1,
      this.withFocusedBorder = true,
      this.textWithHorizontalPadding = false,
      this.hintColor})
      : super(key: key);
  final _focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: textWithHorizontalPadding
          ? const EdgeInsets.fromLTRB(12, 0, 12, 0)
          : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          label != null
              ? Text(label!,
                  style: ThemeManager.medium(
                      size: 14, color: color ?? AppColors.black))
              : const SizedBox.shrink(),
          const SizedBox(height: 7),
          SizedBox(
            width: width ?? MediaQuery.sizeOf(context).width,
            //height: height ?? 49,
            child: TextFormField(
              onTapOutside: (event) =>
                  FocusManager.instance.primaryFocus?.canRequestFocus,
              cursorColor: AppColors.primary,
              //focusNode: focusNode,
              maxLines: maxLines,
              controller: controller,
              enabled: enabled,
              obscureText: obscureText,
              keyboardType: keyboardType ?? TextInputType.text,
              //style: TextStyle(color: color),
              textAlignVertical:
                  prefix != null ? TextAlignVertical.center : null,
              decoration: InputDecoration(
                  disabledBorder: null,
                  enabledBorder: null,
                  errorMaxLines: errorMaxLines,
                  errorStyle: TextStyle(
                    color: Theme.of(context).colorScheme.error,
                  ),
                  border: withFocusedBorder
                      ? OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10.0),
                          borderSide:
                              const BorderSide(color: AppColors.borderGrey),
                        )
                      : InputBorder.none,
                  // errorBorder: withFocusedBorder
                  //     ? const OutlineInputBorder(
                  //         borderRadius: BorderRadius.all(Radius.circular(25)),
                  //         borderSide: BorderSide(color: AppColors.primary),
                  //       )
                  //     : null,
                  // focusedErrorBorder: withFocusedBorder
                  //     ? const OutlineInputBorder(
                  //         borderRadius: BorderRadius.all(Radius.circular(25)),
                  //         borderSide: BorderSide(color: AppColors.primary),
                  //       )
                  //     : null,
                  focusedBorder: withFocusedBorder
                      ? const OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(10)),
                          borderSide: BorderSide(color: AppColors.primary),
                        )
                      : null,
                  hintText: hint,
                  hintStyle: ThemeManager.medium(
                      size: 14, color: hintColor ?? Colors.black38),
                  contentPadding: EdgeInsets.symmetric(
                    // horizontal: suffix != null && !enabled ? 8 : 0,
                      vertical: addHintVerticalPadding ? 8 : 0,
                      horizontal: 12),
                  suffixIcon: suffix,
                  suffixIconConstraints: const BoxConstraints(maxWidth: 60),
                  prefixIcon: prefix),
              onChanged: (val) {
                if (onChange != null) onChange!();
                log("onFieldSubmitted $val");
                if (onFieldSubmitted != null) onFieldSubmitted!(val);
              },
              validator: validator as String? Function(String?)?,
            ),
          )
        ],
      ),
    );
  }
}
