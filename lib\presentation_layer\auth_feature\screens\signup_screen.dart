import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:arepsalin/data_layer/service/authentication_service.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_images.dart';
import '../../../data_layer/provider/authentication_provider.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_form_field.dart';
import '../../widgets/image_input.dart';
import '../../widgets/snackbar_widget.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final TextEditingController _emailController = TextEditingController();

  final TextEditingController _passwordController = TextEditingController();

  final TextEditingController _nameController = TextEditingController();

  final TextEditingController _phoneController = TextEditingController();

  final TextEditingController _addressController = TextEditingController();

  final TextEditingController _fatherController = TextEditingController();

  final TextEditingController _churchServiceController =
      TextEditingController();

  final TextEditingController _qualificationController =
      TextEditingController();

  final TextEditingController _deaconessRankController =
      TextEditingController();

  final TextEditingController _jobController = TextEditingController();

  final TextEditingController _birthdateController = TextEditingController();

  final AuthenticationService _authenticationService = AuthenticationService();

  //late String userBirthdate;

  @override
  void dispose() {
    _birthdateController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _churchServiceController.dispose();
    _qualificationController.dispose();
    _deaconessRankController.dispose();
    _fatherController.dispose();
    _phoneController.dispose();
    _jobController.dispose();
    _addressController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return LocaleKeys.enterValidEmail.tr();
    }
    final emailRegex = RegExp(r'^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$');

    if (!emailRegex.hasMatch(value)) {
      return LocaleKeys.enterValidEmail.tr();
    }

    return null;
  }

  String? isValidQuadName(String? name) {
    List<String>? names = name?.split(' ');

    if (name == null || name.isEmpty) {
      return LocaleKeys.enterFullName.tr();
    }
    if (names?.length != 4) {
      return LocaleKeys.enterFullName.tr();
    }

    const pattern = r'^[؀-ۿ]+$';

    final regExp = RegExp(pattern);

    for (int i = 0; i < names!.length; i++) {
      names[i] = names[i].trim();
    }

    for (String individualName in names) {
      if (!regExp.hasMatch(individualName)) {
        return LocaleKeys.enterValidName.tr();
      }
    }

    return null;
  }

  File? _selectedImage;

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.sizeOf(context);
    return Scaffold(
      appBar: CustomAppBar(
        backIcon: false,
        title: LocaleKeys.createNewAccount.tr(),
        elevation: 0,
        backColor: AppColors.transparent,
      ),
      body: SingleChildScrollView(
        child: Container(
          decoration: const BoxDecoration(color: AppColors.white),
          padding: const EdgeInsets.only(bottom: 20, right: 16, left: 16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Center(
                  child: SizedBox(
                      width: size.width - 100,
                      child: Image(
                          image: AssetImage(AppImages.logo),
                          fit: BoxFit.contain)),
                ),
                ImageInput(
                  onPickImage: (image) {
                    _selectedImage = image;
                  },
                ),
                const SizedBox(height: 15),
                CustomTextFormField(
                  label: LocaleKeys.name.tr(),
                  hint: LocaleKeys.enterFullName.tr(),
                  controller: _nameController,
                  validator: isValidQuadName,
                  withBorder: true,
                ),
                const SizedBox(height: 15),
                CustomTextFormField(
                  label: LocaleKeys.phoneNumber.tr(),
                  hint: LocaleKeys.enterPhoneNumber.tr(),
                  controller: _phoneController,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return LocaleKeys.enterValidName.tr();
                    }
                    return null;
                  },
                  withBorder: true,
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 15),
                CustomTextFormField(
                  label: LocaleKeys.address.tr(),
                  hint: LocaleKeys.enterAddress.tr(),
                  controller: _addressController,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return LocaleKeys.enterValidName.tr();
                    }
                    return null;
                  },
                  withBorder: true,
                ),
                const SizedBox(height: 15),
                CustomTextFormField(
                  label: LocaleKeys.confessionFather.tr(),
                  hint: LocaleKeys.enterConfessionFather.tr(),
                  controller: _fatherController,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return LocaleKeys.enterValidName.tr();
                    }
                    return null;
                  },
                  withBorder: true,
                ),
                const SizedBox(height: 15),
                CustomTextFormField(
                  label: LocaleKeys.churchService.tr(),
                  hint: LocaleKeys.enterChurchService.tr(),
                  controller: _churchServiceController,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return LocaleKeys.enterValidName.tr();
                    }
                    return null;
                  },
                  withBorder: true,
                ),
                const SizedBox(height: 15),
                CustomTextFormField(
                  label: LocaleKeys.qualification.tr(),
                  hint: LocaleKeys.enterQualification.tr(),
                  controller: _qualificationController,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return LocaleKeys.enterValidName.tr();
                    }
                    return null;
                  },
                  withBorder: true,
                ),
                const SizedBox(height: 15),
                CustomTextFormField(
                  label: LocaleKeys.deaconessRank.tr(),
                  hint: LocaleKeys.enterDeaconessRank.tr(),
                  controller: _deaconessRankController,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return LocaleKeys.enterValidName.tr();
                    }
                    return null;
                  },
                  withBorder: true,
                ),
                const SizedBox(height: 15),
                CustomTextFormField(
                  label: LocaleKeys.job.tr(),
                  hint: LocaleKeys.enterJob.tr(),
                  controller: _jobController,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return LocaleKeys.enterValidName.tr();
                    }
                    return null;
                  },
                  withBorder: true,
                ),
                const SizedBox(height: 15),
                Text(
                  LocaleKeys.birthBate.tr(),
                  style: ThemeManager.medium(size: 14, color: AppColors.black),
                ),
                const SizedBox(height: 7),
                Consumer<AuthenticationProvider>(
                  builder: (context, signUpProvider, child) {
                    return CustomButton(
                        onPressed: () {
                          showCupertinoModalPopup(
                            context: context,
                            builder: (BuildContext context) {
                              return Container(
                                height:
                                    MediaQuery.of(context).size.height * 0.35,
                                color: Colors.white,
                                child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    Container(
                                      color: CupertinoColors.systemBackground,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          CupertinoButton(
                                            onPressed: () {
                                              signUpProvider.setSelectedDate(
                                                  DateTime.now());
                                              Navigator.of(context).pop();
                                            },
                                            child: Text(LocaleKeys.cancel.tr()),
                                          ),
                                          CupertinoButton(
                                            onPressed: () {
                                              _birthdateController.text =
                                                  "${signUpProvider.selectedDate?.year}-${signUpProvider.selectedDate?.month}-${signUpProvider.selectedDate?.day}";
                                              Navigator.of(context).pop();
                                            },
                                            child: Text(LocaleKeys.save.tr()),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: CupertinoDatePicker(
                                        onDateTimeChanged: (DateTime value) {
                                          signUpProvider.setSelectedDate(value);
                                        },
                                        mode: CupertinoDatePickerMode.date,
                                        initialDateTime:
                                            signUpProvider.selectedDate,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ).then((value) {
                            if (value != null) {
                              // The user selected a date, you can update your UI element here
                              // For example, set the selected date to your Text widget
                              _birthdateController.text =
                                  DateFormat('yyyy-MM-dd')
                                      .format(signUpProvider.selectedDate!);
                            }
                          });
                        },
                        borderColor: AppColors.transparent,
                        btnColor: AppColors.transparent,
                        textStyle: ThemeManager.medium(
                            size: 14, color: AppColors.black),
                        title: signUpProvider.selectedDate == null
                            ? LocaleKeys.enterBirthdate.tr()
                            : "${signUpProvider.selectedDate?.year}-${signUpProvider.selectedDate?.month}-${signUpProvider.selectedDate?.day}");
                  },
                ),
                //const SizedBox(height: 7),
                CustomTextFormField(
                  label: LocaleKeys.email.tr(),
                  hint: LocaleKeys.enterEmail.tr(),
                  keyboardType: TextInputType.emailAddress,
                  controller: _emailController,
                  validator: validateEmail,
                  withBorder: true,
                ),
                const SizedBox(height: 15),
                Consumer<AuthenticationProvider>(
                    builder: (context, provider, child) {
                  return CustomTextFormField(
                    suffix: Padding(
                      padding:
                          const EdgeInsetsDirectional.only(start: 15, end: 15),
                      child: InkWell(
                        child: provider.showPassword
                            ? const Icon(
                                Icons.visibility_off_outlined,
                                color: AppColors.borderGrey,
                                size: 22,
                              )
                            : const Icon(
                                Icons.visibility_outlined,
                                color: AppColors.borderGrey,
                                size: 22,
                              ),
                        onTap: () {
                          //print(provider.showPassword);
                          provider.togglePasswordVisibility();
                        },
                      ),
                    ),
                    label: LocaleKeys.pass.tr(),
                    hint: LocaleKeys.enterPass.tr(),
                    obscureText: provider.showPassword,
                    controller: _passwordController,
                    validator: (value) {
                      if (value == null || value.length < 8) {
                        return LocaleKeys.passValidation.tr();
                      }
                      return null;
                    },
                    withBorder: true,
                  );
                }),

                const SizedBox(height: 25),
                CustomButton(
                  height: 50,
                  borderRadius: 10,
                  onPressed: () {
                    if (_selectedImage == null) {
                      SnackBarWidget(
                        message: LocaleKeys.choseImage.tr(),
                        context: context,
                        backgroundColor: AppColors.black,
                        messageColor: AppColors.white,
                        messageSize: 16,
                      ).showCustomSnackBar();
                    }
                    if (_selectedImage != null) {
                      _authenticationService.signUp(
                        context: context,
                        formKey: _formKey,
                        email: _emailController.text,
                        name: _nameController.text,
                        pass: _passwordController.text,
                        address: _addressController.text,
                        job: _jobController.text,
                        phone: _phoneController.text,
                        father: _fatherController.text,
                        rank: _deaconessRankController.text,
                        qualification: _qualificationController.text,
                        service: _churchServiceController.text,
                        birthDate: _birthdateController.text,
                        imageFile: _selectedImage!,
                      );
                    }
                  },
                  title: LocaleKeys.register.tr(),
                  textStyle:
                      ThemeManager.semiBold(color: AppColors.white, size: 16),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
