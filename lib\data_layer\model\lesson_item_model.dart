class LessonItem {
  final int id;
  final String title;
  final String itemType;
  final String itemContent;

  LessonItem({
    required this.id,
    required this.title,
    required this.itemType,
    required this.itemContent,
  });

  factory LessonItem.fromJson(Map<String, dynamic> json) {
    return LessonItem(
      id: json['id'],
      title: json['title'],
      itemType: json['itemType'],
      itemContent: json['itemContent'],
    );
  }
}