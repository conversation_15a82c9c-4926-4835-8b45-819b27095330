import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/data_layer/model/announcement_model.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/presentation_layer/home_feature/screens/announcement_details_screen.dart';

class AnnouncementCard extends StatelessWidget {
  final Announcement announcement;

  const AnnouncementCard({Key? key, required this.announcement}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AnnouncementDetailsScreen(
              announcement: announcement,
            ),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.bermuda.withOpacity(0.1),
              blurRadius: 12,
              offset: const Offset(0, 4),
              spreadRadius: 2,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image section with gradient overlay
              if (announcement.imageUrl != null)
                Stack(
                  children: [
                    SizedBox(
                      height: 80,
                      width: double.infinity,
                      child: Image.network(
                        announcement.imageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: AppColors.bermuda.withOpacity(0.2),
                            child: const Center(
                              child: Icon(
                                Icons.image_not_supported_outlined,
                                color: AppColors.bermuda,
                                size: 30,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    // Gradient overlay
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              AppColors.black.withOpacity(0.5),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // Date badge
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          DateFormat('MMM d').format(announcement.createdAt),
                          style: ThemeManager.medium(
                            size: 10,
                            color: AppColors.bermuda,
                          ),
                        ),
                      ),
                    ),
                    // Meeting icon if available
                    if (announcement.meetingLink != null)
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: AppColors.bermuda,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.black.withOpacity(0.2),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.video_call_rounded,
                            color: AppColors.white,
                            size: 16,
                          ),
                        ),
                      ),
                  ],
                ),
              
              // Content section
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title with meeting icon
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              announcement.title,
                              style: ThemeManager.bold(
                                size: 16,
                                color: AppColors.black,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (announcement.meetingLink != null && announcement.imageUrl == null)
                            Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: AppColors.bermuda,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.video_call_rounded,
                                color: AppColors.white,
                                size: 14,
                              ),
                            ),
                        ],
                      ),
                      
                      // Date if no image
                      if (announcement.imageUrl == null)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            DateFormat('MMMM d, yyyy').format(announcement.createdAt),
                            style: ThemeManager.regular(
                              size: 12,
                              color: AppColors.grey,
                            ),
                          ),
                        ),
                      
                      const SizedBox(height: 6),
                      
                      // Description
                      Expanded(
                        child: Text(
                          announcement.description,
                          style: ThemeManager.medium(
                            size: 13,
                            color: AppColors.black.withOpacity(0.7),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      
                      // Read more button
                      Container(
                        alignment: Alignment.centerRight,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppColors.bermuda.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Read more',
                                style: ThemeManager.medium(
                                  size: 12,
                                  color: AppColors.bermuda,
                                ),
                              ),
                              const SizedBox(width: 4),
                              const Icon(
                                Icons.arrow_forward_ios,
                                size: 10,
                                color: AppColors.bermuda,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}



