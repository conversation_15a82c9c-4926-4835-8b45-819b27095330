class ResponseWrapper {
  final bool succeeded;
  final int statusCode;
  dynamic response;
  Error? error;
  List<dynamic>? args;

  ResponseWrapper.success({this.response})
      : statusCode = 200,
        succeeded = true;

  ResponseWrapper(
    this.succeeded,
    this.statusCode, {
    this.response,
    this.error,
    this.args,
  });

  T unwrap<T>() => response as T;
}

class Error {
  final String error;
  final String message;

  Error({required this.error, required this.message});

  @override
  String toString() => message;
}
