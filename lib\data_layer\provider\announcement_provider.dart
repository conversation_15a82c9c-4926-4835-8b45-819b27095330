import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:arepsalin/constants/variables.dart';
import 'package:arepsalin/data_layer/model/announcement_model.dart';
import 'package:arepsalin/managers/secure_storage.dart';

class AnnouncementProvider extends ChangeNotifier {
  List<Announcement> _announcements = [];
  bool _isLoading = false;
  String? _error;

  List<Announcement> get announcements => _announcements;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> fetchAnnouncements() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await SecureStorage.checkTokenExpired();
      String? accessToken = await SecureStorage().getToken("access_token");
      
      final url = Uri.parse('${Variables.baseUrl}/announcement/general');
      
      final response = await http.get(
        url,
        headers: {
          "Content-Type": "application/json",
          if (accessToken != null) "Authorization": "Bearer $accessToken",
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['data'] != null) {
          _announcements = (data['data'] as List)
              .map((item) => Announcement.fromJson(item))
              .toList();
        } else {
          _announcements = [];
        }
      } else {
        _error = 'Failed to load announcements';
      }
    } catch (e) {
      _error = 'Error: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}