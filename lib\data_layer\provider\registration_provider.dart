import 'dart:io';

import 'package:arepsalin/data_layer/model/registration_model.dart';
import 'package:flutter/material.dart';

class RegistrationProvider extends ChangeNotifier {
  // Current step in the registration process
  int _currentStep = 0;

  // Registration data model
  final RegistrationModel _registrationData = RegistrationModel();

  // Loading state
  bool _isLoading = false;

  // Gender options
  final List<String> genderOptions = ['male', 'female'];

  // Role options
  final List<String> roleOptions = ['student', 'teacher', 'admin'];

  // Getters
  int get currentStep => _currentStep;

  RegistrationModel get registrationData => _registrationData;

  bool get isLoading => _isLoading;

  // Step validation getters
  bool get isStep1Valid => _registrationData.isStep1Valid();

  bool get isStep2Valid => _registrationData.isStep2Valid();

  bool get isStep3Valid => _registrationData.isStep3Valid();

  bool get isStep4Valid => _registrationData.isStep4Valid();

  bool get isStep5Valid => _registrationData.isStep5Valid();

  bool get isAllValid => _registrationData.isAllValid();

  // Set loading state
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Move to the next step
  void nextStep() {
    if (_currentStep < 4) {
      _currentStep++;
      notifyListeners();
    }
  }

  // Move to the previous step
  void previousStep() {
    if (_currentStep > 0) {
      _currentStep--;
      notifyListeners();
    }
  }

  // Go to a specific step
  void goToStep(int step) {
    if (step >= 0 && step <= 4) {
      _currentStep = step;
      notifyListeners();
    }
  }

  // Update email
  void updateEmail(String email) {
    _registrationData.email = email;
    notifyListeners();
  }

  // Update password
  void updatePassword(String password) {
    _registrationData.password = password;
    notifyListeners();
  }

  // Update name
  void updateName(String name) {
    _registrationData.name = name;
    notifyListeners();
  }

  // Update phone
  void updatePhone(String phone) {
    _registrationData.phone = phone;
    notifyListeners();
  }

  // Update birthday
  void updateBirthday(String birthday) {
    _registrationData.birthday = birthday;
    notifyListeners();
  }

  // Update nationality
  void updateNationality(String nationality) {
    _registrationData.nationality = nationality;
    notifyListeners();
  }

  // Update address
  void updateAddress(String address) {
    _registrationData.address = address;
    notifyListeners();
  }

  // Update gender
  void updateGender(String gender) {
    _registrationData.gender = gender;
    notifyListeners();
  }

  // Update role
  void updateRole(String role) {
    _registrationData.role = role;
    notifyListeners();
  }

  // Update city
  void updateCity(String city) {
    _registrationData.city = city;
    notifyListeners();
  }

  // Update church
  void updateChurch(String church) {
    _registrationData.church = church;
    notifyListeners();
  }

  // Update abEle3traf
  void updateAbEle3traf(String abEle3traf) {
    _registrationData.abEle3traf = abEle3traf;
    notifyListeners();
  }

  // Update deaconLevel
  void updateDeaconLevel(String deaconLevel) {
    _registrationData.deaconLevel = deaconLevel;
    notifyListeners();
  }

  // Update churchService
  void updateChurchService(String churchService) {
    _registrationData.churchService = churchService;
    notifyListeners();
  }

  // Update qualifications
  void updateQualifications(String qualifications) {
    _registrationData.qualifications = qualifications;
    notifyListeners();
  }

  // Update profile image file
  void updateProfileImageFile(File file) {
    _registrationData.profileImageFile = file;
    notifyListeners();
  }

  // Update personal ID front file
  void updatePersonalIDFrontFile(File file) {
    _registrationData.personalIDFrontFile = file;
    notifyListeners();
  }

  // Update personal ID back file
  void updatePersonalIDBackFile(File file) {
    _registrationData.personalIDBackFile = file;
    notifyListeners();
  }

  // Update tazkia file
  void updateTazkiaFile(File file) {
    _registrationData.tazkiaFile = file;
    notifyListeners();
  }

  // Reset all data
  void resetData() {
    _currentStep = 0;
    _registrationData.email = null;
    _registrationData.password = null;
    _registrationData.phone = null;
    _registrationData.name = null;
    _registrationData.birthday = null;
    _registrationData.nationality = null;
    _registrationData.address = null;
    _registrationData.gender = null;
    _registrationData.role = null;
    _registrationData.profilePicture = null;
    _registrationData.city = null;
    _registrationData.church = null;
    _registrationData.abEle3traf = null;
    _registrationData.deaconLevel = null;
    _registrationData.churchService = null;
    _registrationData.qualifications = null;
    _registrationData.personalIDFront = null;
    _registrationData.personalIDBack = null;
    _registrationData.tazkia = null;
    _registrationData.profileImageFile = null;
    _registrationData.personalIDFrontFile = null;
    _registrationData.personalIDBackFile = null;
    _registrationData.tazkiaFile = null;
    notifyListeners();
  }
}
