import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:provider/provider.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/data_layer/provider/new_quiz_provider.dart';
import 'package:arepsalin/data_layer/model/quiz_model.dart';
import '../../widgets/custom_button.dart';
import 'audio_recorder.dart';
import 'audio_player.dart';

class RecordQuizWidget extends StatefulWidget {
  final Quiz quiz;
  final VoidCallback onSubmit;

  const RecordQuizWidget({
    super.key,
    required this.quiz,
    required this.onSubmit,
  });

  @override
  State<RecordQuizWidget> createState() => _RecordQuizWidgetState();
}

class _RecordQuizWidgetState extends State<RecordQuizWidget> {
  bool _showRecorder = false;
  bool _showPlayer = false;
  String? _audioPath;

  @override
  Widget build(BuildContext context) {
    final themeColor = widget.quiz.isFinal ? AppColors.bermuda : AppColors.primary;

    return Consumer<NewQuizProvider>(
      builder: (context, quizProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 10),

            // Quiz Header
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                ],
                border: Border.all(
                  color: themeColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.mic,
                    size: 48,
                    color: themeColor,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    widget.quiz.name,
                    style: ThemeManager.bold(
                      size: 18,
                      color: AppColors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'اختبار تسجيل صوتي',
                    style: ThemeManager.medium(
                      size: 14,
                      color: themeColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: themeColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.star,
                          color: themeColor,
                          size: 18,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${widget.quiz.grade} نقطة',
                          style: ThemeManager.semiBold(
                            size: 14,
                            color: themeColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Question Section
            if (widget.quiz.content.isNotEmpty)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      spreadRadius: 1,
                    ),
                  ],
                  border: Border.all(
                    color: themeColor.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: themeColor.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: themeColor.withOpacity(0.2),
                        ),
                      ),
                      child: Text(
                        widget.quiz.content.first.question,
                        style: ThemeManager.semiBold(
                          size: 16,
                          color: AppColors.black,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'الدرجة: ${widget.quiz.content.first.grade}',
                      style: ThemeManager.medium(
                        size: 14,
                        color: AppColors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

            // Audio Recording/Upload Section
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                    if (!_showRecorder && !_showPlayer) ...[
                      Icon(
                        Icons.audiotrack,
                        size: 60,
                        color: themeColor.withOpacity(0.7),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'اختر طريقة الإجابة',
                        style: ThemeManager.bold(
                          size: 18,
                          color: AppColors.black,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),

                      // Record Audio Button
                      CustomButton(
                        title: 'تسجيل صوتي',
                        onPressed: () {
                          setState(() {
                            _showRecorder = true;
                            _showPlayer = false;
                          });
                        },
                        btnColor: themeColor,
                        textStyle: ThemeManager.semiBold(
                          size: 16,
                          color: Colors.white,
                        ),
                        height: 48,
                        borderRadius: 12,
                        icon: Icons.mic,
                      ),

                      const SizedBox(height: 12),

                      // Upload MP3 Button
                      CustomButton(
                        title: 'رفع ملف MP3',
                        onPressed: _pickAudioFile,
                        btnColor: Colors.transparent,
                        borderColor: themeColor,
                        textStyle: ThemeManager.semiBold(
                          size: 14,
                          color: themeColor,
                        ),
                        height: 40,
                        borderRadius: 12,
                        icon: Icons.upload_file,
                      ),
                    ],

                    // Audio Recorder
                    if (_showRecorder && !_showPlayer)
                      Column(
                        children: [
                          Text(
                            'اضغط على الميكروفون لبدء التسجيل',
                            style: ThemeManager.medium(
                              size: 14,
                              color: AppColors.grey,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          AudioRecorderWidget(
                            onStop: (path) {
                              setState(() {
                                _audioPath = path;
                                _showRecorder = false;
                                _showPlayer = true;
                              });
                              quizProvider.setAudioFile(path);
                            },
                          ),
                          const SizedBox(height: 16),
                          CustomButton(
                            title: 'إلغاء',
                            onPressed: () {
                              setState(() {
                                _showRecorder = false;
                              });
                            },
                            btnColor: Colors.transparent,
                            borderColor: AppColors.grey,
                            textStyle: ThemeManager.semiBold(
                              size: 14,
                              color: AppColors.grey,
                            ),
                            height: 36,
                            borderRadius: 10,
                          ),
                        ],
                      ),

                    // Audio Player
                    if (_showPlayer && _audioPath != null)
                      Column(
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 48,
                            color: AppColors.primary,
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'تم التسجيل بنجاح',
                            style: ThemeManager.bold(
                              size: 16,
                              color: AppColors.primary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          CustomAudioPlayer(
                            source: _audioPath!,
                            onDelete: () {
                              setState(() {
                                _showPlayer = false;
                                _audioPath = null;
                              });
                              quizProvider.clearAudioFile();
                            },
                          ),
                          const SizedBox(height: 16),
                          CustomButton(
                            title: 'تسجيل جديد',
                            onPressed: () {
                              setState(() {
                                _showPlayer = false;
                                _showRecorder = true;
                                _audioPath = null;
                              });
                              quizProvider.clearAudioFile();
                            },
                            btnColor: Colors.transparent,
                            borderColor: themeColor,
                            textStyle: ThemeManager.semiBold(
                              size: 14,
                              color: themeColor,
                            ),
                            height: 36,
                            borderRadius: 10,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Submit Button
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: CustomButton(
                title: 'إرسال الإجابة',
                onPressed: quizProvider.hasAudioFile() ? widget.onSubmit : null,
                btnColor: quizProvider.hasAudioFile()
                    ? themeColor
                    : themeColor.withOpacity(0.5),
                textStyle: ThemeManager.semiBold(
                  size: 16,
                  color: Colors.white,
                ),
                height: 48,
                borderRadius: 12,
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _pickAudioFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowedExtensions: ['mp3', 'm4a', 'wav', 'aac'],
      );

      if (result != null && result.files.single.path != null) {
        final filePath = result.files.single.path!;

        if (!mounted) return;

        setState(() {
          _audioPath = filePath;
          _showPlayer = true;
          _showRecorder = false;
        });

        final quizProvider = Provider.of<NewQuizProvider>(context, listen: false);
        quizProvider.setAudioFile(filePath);
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في اختيار الملف: $e'),
          backgroundColor: AppColors.red,
        ),
      );
    }
  }
}
