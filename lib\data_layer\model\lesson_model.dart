import 'lesson_item_model.dart';
import 'week_quiz_model.dart';

class Lesson {
  final int id;
  final String name;
  final List<LessonItem> items;
  final List<WeekQuiz> quizzes;
  final int order;

  Lesson({
    required this.id,
    required this.name,
    required this.items,
    this.quizzes = const [],
    required this.order,
  });

  factory Lesson.fromJson(Map<String, dynamic> json) {
    return Lesson(
      id: json['id'],
      name: json['name'],
      items: (json['items'] as List)
          .map((item) => LessonItem.fromJson(item))
          .toList(),
      quizzes: json['quizzes'] != null
          ? (json['quizzes'] as List)
              .map((quiz) => WeekQuiz.fromJson(quiz))
              .toList()
          : [],
      order: json['order'],
    );
  }
}