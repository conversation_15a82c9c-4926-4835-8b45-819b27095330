import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../../managers/secure_storage.dart';

import '../model/enrolled_semester.dart';
import '../model/semester_model.dart';
import '../model/week_quiz_model.dart';
import '../model/week_response.dart';
import '../model/week_subject_model.dart';

class SemesterService {
  final String baseUrl = 'https://aripsalin-api-production.up.railway.app';
  final storage = SecureStorage();

  Future<List<EnrolledSemester>> getEnrolledSemesters() async {
    try {
      final token = await storage.getToken("access_token");

      final response = await http.get(
        Uri.parse('$baseUrl/semester/enrolled'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> semesterList = jsonDecode(response.body);
        return semesterList
            .map((json) => EnrolledSemester.fromJson(json))
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error fetching enrolled semesters: $e');
      return [];
    }
  }

  Future<List<WeekResponse>> getSemesterWeeksById(int semesterId) async {
    try {
      final token = await storage.getToken("access_token");

      final response = await http.get(
        Uri.parse('$baseUrl/semester/$semesterId/weeks-list'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> weeksList = jsonDecode(response.body);
        return weeksList.map((json) => WeekResponse.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error fetching semester weeks: $e');
      return [];
    }
  }

  Future<List<WeekSubject>> getWeekSubjects(int weekId) async {
    try {
      final token = await storage.getToken("access_token");

      final response = await http.get(
        Uri.parse('$baseUrl/semester/week/$weekId/subjects'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        final Map<String, dynamic> data =
            responseData['data'] as Map<String, dynamic>;
        final List<dynamic> subjectsData = data['subjects'] as List<dynamic>;
        final subjectsD =subjectsData.map((json) => WeekSubject.fromJson(json)).toList();
        print(subjectsData);
        return subjectsD ;
      }

      throw Exception('Failed to fetch week subjects: ${response.statusCode}');
    } catch (e) {
      debugPrint('Error fetching week subjects: $e');
      return [];
    }
  }


}
//   Future<List<Semester>> getAllSemesters() async {
//     QuerySnapshot querySnapshot = await semesterCollection.get();
//     return querySnapshot.docs.map((doc) {
//       return Semester.fromSnapshot(doc);
//     }).toList();
//   }
//
//   Future<Semester> getSemesterBySemesterNo(int semesterNo) async {
//     QuerySnapshot querySnapshot = await semesterCollection
//         .where('semesterNo', isEqualTo: semesterNo)
//         .get();
//
//     if (querySnapshot.docs.isNotEmpty) {
//       return Semester.fromSnapshot(querySnapshot.docs.first);
//     } else {
//       throw Exception('Semester not found');
//     }
//   }
// }
