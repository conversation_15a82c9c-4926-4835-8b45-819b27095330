name: are<PERSON>alin
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  http: ^1.3.0
  smooth_page_indicator: ^1.2.1


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  shared_preferences: ^2.5.2
  cached_network_image: ^3.4.1
  badges: ^3.1.2
  easy_localization: ^3.0.7+1
  provider: ^6.1.2
  persistent_bottom_nav_bar: ^6.2.1
  firebase_auth: ^5.3.0
  firebase_core: ^3.5.0
  cloud_firestore: ^5.4.2
  image_picker: ^1.1.2
  firebase_storage: ^12.3.1
  just_audio: ^0.9.46
  group_radio_button: ^1.3.0
  syncfusion_flutter_pdfviewer: ^28.2.5
  permission_handler: ^11.3.1
  record: ^5.2.0
  audioplayers: ^6.1.2
  just_audio_background: ^0.0.1-beta.15
  flutter_expanded_tile: ^0.4.0
  youtube_player_flutter: ^9.1.1
  file_picker: ^8.1.2
  no_screenshot: ^0.3.1

  flutter_secure_storage: ^9.2.4
  #  firebase_app_check: ^0.2.1+3
  path: any
  flutter_cache_manager: ^3.4.1
  firestore_cache: ^2.14.1
  jwt_decoder: ^2.0.1



dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3
  flutter_native_splash: ^2.3.10

flutter_launcher_icons:
  android: "logo_icon"
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/logo.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"

flutter_native_splash:
  color: "#FFFFFF"
  image: "assets/images/splash_icon.png"
  android: true
  ios: true
  web: false

  android_12:
    image: "assets/images/splash_icon.png"
    icon_background_color: "#FFFFFF"

  android_gravity: center
  ios_content_mode: center
  fullscreen: false


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/fonts/
    - assets/translations/
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: cairo
      fonts:
        - asset: assets/fonts/cairo_extra_light.ttf
          weight: 200
        - asset: assets/fonts/cairo_light.ttf
          weight: 300
        - asset: assets/fonts/cairo_regular.ttf
          weight: 400
        - asset: assets/fonts/cairo_medium.ttf
          weight: 500
        - asset: assets/fonts/cairo_semi_bold.ttf
          weight: 600
        - asset: assets/fonts/cairo_bold.ttf
          weight: 700
        - asset: assets/fonts/cairo_extra_bold.ttf
          weight: 800
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
