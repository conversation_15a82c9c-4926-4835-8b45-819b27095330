class Week {
  int id;
  DateTime startDate;
  DateTime endDate;

  Week({
    required this.id,
    required this.startDate,
    required this.endDate,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
    };
  }

  factory Week.fromJson(Map<String, dynamic> json) {
    return Week(
      id: json['id'] as int,
      startDate: json['startDate'] is String
          ? DateTime.parse(json['startDate'])
          : DateTime.fromMillisecondsSinceEpoch(
              (json['startDate'] as int) * 1000),
      endDate: json['endDate'] is String
          ? DateTime.parse(json['endDate'])
          : DateTime.fromMillisecondsSinceEpoch(
              (json['endDate'] as int) * 1000),
    );
  }
}
