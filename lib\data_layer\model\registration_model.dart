import 'dart:io';

class RegistrationModel {
  String? email;
  String? password;
  String? phone;
  String? name;
  String? birthday;
  String? nationality;
  String? address;
  String? gender;
  String? role;
  String? profilePicture;
  String? city;
  String? church;
  String? abEle3traf;
  String? deaconLevel;
  String? churchService;
  String? qualifications;
  String? personalIDFront;
  String? personalIDBack;
  String? tazkia;

  // Files for upload
  File? profileImageFile;
  File? personalIDFrontFile;
  File? personalIDBackFile;
  File? tazkiaFile;

  RegistrationModel({
    this.email,
    this.password,
    this.phone,
    this.name,
    this.birthday,
    this.nationality,
    this.address,
    this.gender,
    this.role,
    this.profilePicture,
    this.city,
    this.church,
    this.abEle3traf,
    this.deaconLevel,
    this.churchService,
    this.qualifications,
    this.personalIDFront,
    this.personalIDBack,
    this.tazkia,
    this.profileImageFile,
    this.personalIDFrontFile,
    this.personalIDBackFile,
    this.tazkiaFile,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'phone': phone,
      'name': name,
      'birthday': birthday,
      'nationality': nationality,
      'Address': address,
      'gender': gender,
      'role': role,
      'profilePicture': profilePicture,
      'city': city,
      'church': church,
      'AbEle3traf': abEle3traf,
      'deaconLevel': deaconLevel,
      'churchService': churchService,
      'qualifications': qualifications,
      'personalIDFront': personalIDFront,
      'personalIDBack': personalIDBack,
      'Tazkia': tazkia,
    };
  }

  bool isStep1Valid() {
    return email != null &&
        password != null &&
        email!.isNotEmpty &&
        password!.isNotEmpty &&
        password!.length >= 8;
  }

  bool isStep2Valid() {
    return name != null &&
        phone != null &&
        birthday != null &&
        name!.isNotEmpty &&
        phone!.isNotEmpty &&
        birthday!.isNotEmpty;
  }

  bool isStep3Valid() {
    return nationality != null &&
        address != null &&
        gender != null &&
        city != null &&
        nationality!.isNotEmpty &&
        address!.isNotEmpty &&
        gender!.isNotEmpty &&
        city!.isNotEmpty;
  }

  bool isStep4Valid() {
    return church != null &&
        abEle3traf != null &&
        deaconLevel != null &&
        churchService != null &&
        qualifications != null &&
        church!.isNotEmpty &&
        abEle3traf!.isNotEmpty &&
        deaconLevel!.isNotEmpty &&
        churchService!.isNotEmpty &&
        qualifications!.isNotEmpty;
  }

  bool isStep5Valid() {
    return profileImageFile != null &&
        personalIDFrontFile != null &&
        personalIDBackFile != null &&
        tazkiaFile != null;
  }

  bool isAllValid() {
    return isStep1Valid() &&
        isStep2Valid() &&
        isStep3Valid() &&
        isStep4Valid() &&
        isStep5Valid();
  }
}
