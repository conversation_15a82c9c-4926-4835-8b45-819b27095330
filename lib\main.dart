import 'package:arepsalin/data_layer/provider/language_provider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'constants/variables.dart';
import 'managers/shared_pref_manager.dart';
import 'my_app.dart';

Future<void> main() async {
  // This is required for flutter_native_splash
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  // Keep the splash screen visible while we initialize the app
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  // Initialize other services
  await EasyLocalization.ensureInitialized();
  SharedPrefManager sharedPrefManager = SharedPrefManager();
  await sharedPrefManager.read('first_time');
  LanguageProvider languageProvider = LanguageProvider();
  languageProvider.loadLanguage();

  // Remove the splash screen when initialization is complete
  FlutterNativeSplash.remove();
  runApp(EasyLocalization(
    supportedLocales: const [
      Locale(Variables.enLangCode),
      Locale(Variables.arLangCode),
    ],
    path: 'assets/translations',
    saveLocale: true,
    startLocale: const Locale(Variables.arLangCode),
    fallbackLocale: const Locale(Variables.arLangCode),
    child: const MyApp(),
  )
      // ChangeNotifierProvider(
      //   create: (context) => languageProvider,
      //   child: Consumer<LanguageProvider>(
      //     builder: (context, languageProvider, child) {
      //       return
      //     },
      //   ),
      // ),
      );
}
