import 'dart:async';
import 'package:flutter/material.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/data_layer/model/announcement_model.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/presentation_layer/home_feature/widgets/announcement_card.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class AnnouncementCarousel extends StatefulWidget {
  final List<Announcement> announcements;
  final bool isLoading;
  final String? error;

  const AnnouncementCarousel({
    Key? key,
    required this.announcements,
    required this.isLoading,
    this.error,
  }) : super(key: key);

  @override
  State<AnnouncementCarousel> createState() => _AnnouncementCarouselState();
}

class _AnnouncementCarouselState extends State<AnnouncementCarousel> {
  final PageController _pageController = PageController();
  Timer? _autoScrollTimer;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _startAutoScroll();
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(AnnouncementCarousel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.announcements.length != oldWidget.announcements.length) {
      _startAutoScroll();
    }
  }

  void _startAutoScroll() {
    _autoScrollTimer?.cancel();
    if (widget.announcements.length > 1) {
      _autoScrollTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
        if (_pageController.hasClients) {
          if (_currentPage < widget.announcements.length - 1) {
            _currentPage++;
          } else {
            _currentPage = 0;
          }
          _pageController.animateToPage(
            _currentPage,
            duration: const Duration(milliseconds: 800),
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return _buildLoadingState();
    }

    if (widget.error != null) {
      return _buildErrorState();
    }

    if (widget.announcements.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // Section title
        Padding(
          padding: const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 4),
          child: Row(
            children: [
              const Icon(
                Icons.announcement_rounded,
                color: AppColors.bermuda,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Announcements',
                style: ThemeManager.bold(
                  size: 18,
                  color: AppColors.black,
                ),
              ),
              const Spacer(),
              if (widget.announcements.length > 1)
                Text(
                  '${_currentPage + 1}/${widget.announcements.length}',
                  style: ThemeManager.medium(
                    size: 12,
                    color: AppColors.grey,
                  ),
                ),
            ],
          ),
        ),
        
        // Carousel
        Container(
          height: 180,
          margin: const EdgeInsets.only(top: 4, bottom: 8),
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.announcements.length,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            itemBuilder: (context, index) {
              return AnnouncementCard(announcement: widget.announcements[index]);
            },
          ),
        ),
        
        // Indicators
        if (widget.announcements.length > 1)
          Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: SmoothPageIndicator(
              controller: _pageController,
              count: widget.announcements.length,
              effect: ExpandingDotsEffect(
                dotHeight: 8,
                dotWidth: 8,
                spacing: 6,
                expansionFactor: 3,
                activeDotColor: AppColors.bermuda,
                dotColor: AppColors.grey.withOpacity(0.3),
              ),
              onDotClicked: (index) {
                _pageController.animateToPage(
                  index,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Container(
      height: 180,
      margin: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.bermuda.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Center(
        child: CircularProgressIndicator(
          color: AppColors.bermuda,
          strokeWidth: 3,
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              LocaleKeys.failedToLoadAnnouncements.tr(),
              style: ThemeManager.medium(
                size: 14,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.grey.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.announcement_outlined,
              color: AppColors.grey.withOpacity(0.5),
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              LocaleKeys.noAnnouncementsAvailable.tr(),
              style: ThemeManager.medium(
                size: 14,
                color: AppColors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}


