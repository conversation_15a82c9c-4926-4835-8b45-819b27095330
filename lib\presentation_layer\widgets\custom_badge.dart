import 'package:badges/badges.dart' as badges;
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class CustomBadge extends StatelessWidget {
  String totalNumbers;
  IconData icon;
  Color? badgeColor;

  CustomBadge(
      {super.key,
      required this.totalNumbers,
      required this.icon,
      this.badgeColor});

  @override
  Widget build(BuildContext context) {
    return badges.Badge(
      position: badges.BadgePosition.topEnd(top: -10, end: -12),
      showBadge: true,
      ignorePointer: false,
      badgeContent: Icon(icon, color: Colors.white, size: 10),
      badgeAnimation: const badges.BadgeAnimation.rotation(
        animationDuration: Duration(seconds: 1),
        colorChangeAnimationDuration: Duration(seconds: 1),
        loopAnimation: false,
        curve: Curves.fastOutSlowIn,
        colorChangeAnimationCurve: Curves.easeInCubic,
      ),
      badgeStyle: badges.BadgeStyle(
        shape: badges.BadgeShape.square,
        badgeColor: Colors.blue,
        padding: const EdgeInsets.all(5),
        borderRadius: BorderRadius.circular(4),
        borderSide: const BorderSide(color: Colors.white, width: 2),
        borderGradient: const badges.BadgeGradient.linear(
            colors: [Colors.red, Colors.black]),
        badgeGradient: const badges.BadgeGradient.linear(
          colors: [Colors.blue, Colors.yellow],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        elevation: 0,
      ),
      child: const Text('Badge'),
    );
  }
}
