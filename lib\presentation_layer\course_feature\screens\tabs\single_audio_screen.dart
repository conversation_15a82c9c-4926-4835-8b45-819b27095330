import 'package:audio_session/audio_session.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:just_audio/just_audio.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:rxdart/rxdart.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/managers/theme_manager.dart';

import '../../../../constants/app_images.dart';
import '../../widgets/control_buttons.dart';
import '../../widgets/seek_bar.dart';

class AudioPlayerSingleton extends AudioPlayer {
  static AudioPlayerSingleton? _instance;

  static AudioPlayerSingleton getInstance() {
    _instance ??= AudioPlayerSingleton();
    return _instance!;
  }
}

class SingleAudioScreen extends StatefulWidget {
  const SingleAudioScreen({Key? key, required this.title, required this.audio})
      : super(key: key);

  final String title;

  final String audio;

  @override
  State<SingleAudioScreen> createState() => _SingleAudioScreenState();
}

class _SingleAudioScreenState extends State<SingleAudioScreen>
    with WidgetsBindingObserver {
  late AudioPlayer _player;

//  final _player = AudioPlayer();
  @override
  void initState() {
    super.initState();
    _player = AudioPlayerSingleton.getInstance();
    ambiguate(WidgetsBinding.instance)!.addObserver(this);
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark));
    _init();
  }

  Future<void> _init() async {
    final session = await AudioSession.instance;
    await session.configure(const AudioSessionConfiguration.speech());
    _player.playbackEventStream.listen((event) {},
        onError: (Object e, StackTrace stackTrace) {
      print('A stream error occurred: $e');
    });
    try {
      // AAC example: https://dl.espressif.com/dl/audio/ff-16b-2c-44100hz.aac
      await _player.setAudioSource(AudioSource.uri(Uri.parse(widget.audio),
          tag: MediaItem(
              id: '0',
              title: widget.title,
              artist: "Aripsalin",
              artUri: Uri.file("assets/images/logo.png"))));
    } catch (e) {
      print("Error loading audio source: $e");
    }
  }

  // @override
  // void dispose() {
  //   ambiguate(WidgetsBinding.instance)!.removeObserver(this);
  //   _player.dispose();
  //   super.dispose();
  // }

  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   if (state == AppLifecycleState.paused) {
  //     _player.stop();
  //   }
  // }

  /// Collects the data useful for displaying in a seek bar, using a handy
  /// feature of rx_dart to combine the 3 streams of interest into one.
  Stream<PositionData> get _positionDataStream =>
      Rx.combineLatest3<Duration, Duration, Duration?, PositionData>(
          _player.positionStream,
          _player.bufferedPositionStream,
          _player.durationStream,
          (position, bufferedPosition, duration) => PositionData(
              position, bufferedPosition, duration ?? Duration.zero));

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        body: Stack(
          children: [
            SizedBox(
              width: double.infinity,
              height: double.infinity,
              child:
                  Image(image: AssetImage(AppImages.david), fit: BoxFit.fill),
            ),
            SingleChildScrollView(
              child: Container(
                width: double.infinity,
                height: MediaQuery.sizeOf(context).height,
                decoration:
                    BoxDecoration(color: AppColors.grey.withOpacity(0.8)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                Text(widget.title,
                                    overflow: TextOverflow.visible,
                                    textAlign: TextAlign.center,
                                    style: ThemeManager.semiBold(
                                        size: 24, color: AppColors.black),
                                    softWrap: true),
                              ],
                            ),
                          ),
                          InkWell(
                              onTap: () => Navigator.of(context).pop(),
                              child: const Icon(
                                Icons.arrow_forward,
                                color: AppColors.black,
                              )),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                        decoration: BoxDecoration(
                            // shape: BoxShape.circle,
                            color: AppColors.grey,
                            borderRadius: BorderRadius.circular(25)),
                        margin: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 35),
                        height: MediaQuery.sizeOf(context).height / 2.2,
                        width: double.infinity,
                        clipBehavior: Clip.hardEdge,
                        child: Image(
                          image: AssetImage(AppImages.david),
                          fit: BoxFit.fill,
                        )),
                    ControlButtons(_player),
                    StreamBuilder<PositionData>(
                      stream: _positionDataStream,
                      builder: (context, snapshot) {
                        final positionData = snapshot.data;
                        return SeekBar(
                          duration: positionData?.duration ?? Duration.zero,
                          position: positionData?.position ?? Duration.zero,
                          bufferedPosition:
                              positionData?.bufferedPosition ?? Duration.zero,
                          onChangeEnd: _player.seek,
                        );
                      },
                    ),
                    const SizedBox(
                      height: 10,
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
