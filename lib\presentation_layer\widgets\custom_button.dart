import 'package:flutter/material.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';

class CustomButton extends StatelessWidget {
  final String title;
  final Widget? trailer;
  final Widget? leading;
  final Color? btnColor;
  final Color? borderColor;
  final Color? titleColor;
  final double? titleSize;

  //final FontWeight? fontWeight;
  final Function? onPressed;
  final double? borderRadius;
  final double? width;
  final double? height;
  final double? horizontalPadding;
  final TextStyle? textStyle;
  final double? borderWidth;

  const CustomButton({
    Key? key,
    required this.title,
    this.trailer,
    this.leading,
    this.btnColor,
    this.titleColor,
    this.titleSize,
    //this.fontWeight,
    required this.onPressed,
    this.borderRadius,
    this.width,
    this.horizontalPadding,
    this.height,
    this.textStyle,
    this.borderColor,
    this.borderWidth,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width ?? MediaQuery.sizeOf(context).width / 2,
      height: height ?? 41,
      padding: EdgeInsets.symmetric(horizontal: horizontalPadding ?? 0),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: btnColor ?? AppColors.primary,
          elevation: 0,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius ?? 25),
              side: BorderSide(
                  color: borderColor ?? AppColors.primary,
                  width: borderWidth ?? 1)),
          enableFeedback: false,
          disabledBackgroundColor: Colors.transparent,
        ),
        onPressed: onPressed == null ? null : () => onPressed!(),
        child: Row(
          mainAxisAlignment: trailer != null
              ? MainAxisAlignment.spaceBetween
              : MainAxisAlignment.center,
          children: [
            if (leading != null) ...[leading!, const SizedBox(width: 12)],
            Text(title,
                textAlign: TextAlign.center,
                style: textStyle ??
                    ThemeManager.semiBold(
                        size: titleSize ?? 14,
                        color: titleColor ?? AppColors.white)),
            trailer != null ? trailer! : const SizedBox.shrink()
          ],
        ),
      ),
    );
  }
}
