import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../constants/app_enum.dart';

class LanguageProvider extends ChangeNotifier {
  String _currentLanguage = '';

  String get currentLanguage => _currentLanguage;

  UserMode _appMode = UserMode.unauthorized;

  UserMode get userMode => _appMode;

  Future<bool> isUserLogin() async {
    var prefs = await SharedPreferences.getInstance();

    return prefs.getBool('login') ?? false;
  }

  changeUserMode(UserMode newUserMode) {
    _appMode = newUserMode;
  }

  loadLanguage() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    _currentLanguage = prefs.getString('language') ?? 'ar';
    notifyListeners();
  }

  Future<bool> isArabic() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    var lang = prefs.getString('language');
    if (lang == 'ar') {
      return true;
    } else {
      return false;
    }
  }

  Future<void> setLanguage(BuildContext context, String newLanguage) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs
        .setString('language', newLanguage)
        .then((value) => context.setLocale(Locale(newLanguage)));
    _currentLanguage = newLanguage;

    notifyListeners();
  }
}
