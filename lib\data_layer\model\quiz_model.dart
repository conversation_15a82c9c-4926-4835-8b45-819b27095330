class QuizSubject {
  final int id;
  final String name;
  final String code;

  QuizSubject({
    required this.id,
    required this.name,
    required this.code,
  });

  factory QuizSubject.fromJson(Map<String, dynamic> json) {
    return QuizSubject(
      id: json['id'],
      name: json['name'],
      code: json['code'],
    );
  }
}

class QuizSemester {
  final int id;
  final String name;
  final DateTime startDate;
  final DateTime endDate;
  final bool isCurrent;

  QuizSemester({
    required this.id,
    required this.name,
    required this.startDate,
    required this.endDate,
    required this.isCurrent,
  });

  factory QuizSemester.fromJson(Map<String, dynamic> json) {
    return QuizSemester(
      id: json['id'],
      name: json['name'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      isCurrent: json['isCurrent'],
    );
  }
}

class QuizLesson {
  final int id;
  final String name;

  QuizLesson({
    required this.id,
    required this.name,
  });

  factory QuizLesson.fromJson(Map<String, dynamic> json) {
    return QuizLesson(
      id: json['id'],
      name: json['name'],
    );
  }
}

class QuizAnswer {
  final int id;
  final String text;

  QuizAnswer({
    required this.id,
    required this.text,
  });

  factory QuizAnswer.fromJson(Map<String, dynamic> json) {
    return QuizAnswer(
      id: json['id'],
      text: json['text'],
    );
  }
}

class QuizQuestion {
  final int questionId;
  final String question;
  final String type;
  final int grade;
  final List<QuizAnswer>? answers;
  final int? correctAnswerId;
  final String? correctAnswer;

  QuizQuestion({
    required this.questionId,
    required this.question,
    required this.type,
    required this.grade,
    this.answers,
    this.correctAnswerId,
    this.correctAnswer,
  });

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    List<QuizAnswer>? answers;
    if (json['answers'] != null) {
      answers = (json['answers'] as List)
          .map((answer) => QuizAnswer.fromJson(answer))
          .toList();
    }

    return QuizQuestion(
      questionId: json['questionId'],
      question: json['question'],
      type: json['type'],
      grade: json['grade'],
      answers: answers,
      correctAnswerId: json['correctAnswerId'],
      correctAnswer: json['correctAnswer'],
    );
  }

  bool isMCQ() => type == 'mcq';
  bool isText() => type == 'text';
}

class Quiz {
  final int id;
  final String name;
  final int grade;
  final String type;
  final int numberOfAttempts;
  final int? timeLimit; // Made optional
  final QuizSubject subject;
  final QuizLesson lesson;
  final List<QuizQuestion> content;
  final bool isRecord; // Added isRecord field

  Quiz({
    required this.id,
    required this.name,
    required this.grade,
    required this.type,
    required this.numberOfAttempts,
    this.timeLimit, // Made optional
    required this.subject,
    required this.lesson,
    required this.content,
    this.isRecord = false, // Added isRecord field with default value
  });

  factory Quiz.fromJson(Map<String, dynamic> json) {
    return Quiz(
      id: json['id'],
      name: json['name'],
      grade: json['grade'],
      type: json['type'],
      numberOfAttempts: json['numberOfAttempts'],
      timeLimit: json['timeLimit'], // Can be null now
      subject: QuizSubject.fromJson(json['subject']),
      lesson: QuizLesson.fromJson(json['lesson']),
      content: (json['content'] as List)
          .map((question) => QuizQuestion.fromJson(question))
          .toList(),
      isRecord: json['isRecord'] ?? false, // Default to false if not present
    );
  }

  bool get isFinal => type.toLowerCase() == 'final';
  int get totalGrade => content.fold(0, (sum, question) => sum + question.grade);

  // Check if the quiz has a timer
  bool get hasTimer => timeLimit != null && !isRecord;

  // Check if this is a record quiz
  bool get isRecordQuiz => isRecord;
}
