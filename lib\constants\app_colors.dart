import 'package:flutter/material.dart';

class AppColors {
  AppColors._();

  static const Color actionsColor = Color(0xff424242);
  static const Color black = Colors.black;
  static const Color red = Color(0xffF22525);
  static const Color borderGrey = Color(0xff979797);
  static const hintGrey = Color(0xff151414);
  static const Color transparent = Colors.transparent;
  static const Color primary = Color.fromRGBO(38, 55, 81, 1);
  static const Color bermuda = Color(0xffd0b285);
  static const Color white = Color.fromRGBO(255, 255, 255, 1);
  static const Color hintText = Color.fromRGBO(21, 20, 20, 0.25);
  static const Color inActiveColor = Color.fromRGBO(21, 20, 20, 1);
  static const Color grey = Colors.grey;
  static const Color warmBlue = Color(0xff5DA5C0);
  static const Color amazingBlack = Color(0xff05161A);
  static const Color darkGrey = Color(0xffdadada);
  static const Color warmWhite = Color(0xFFF3F3F3);
  static const Color lightGrey = Color(0xffEAEAEA);
}
