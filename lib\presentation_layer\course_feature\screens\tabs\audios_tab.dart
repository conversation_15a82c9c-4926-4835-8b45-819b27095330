import 'package:arepsalin/data_layer/model/week_subject_model.dart';
import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../../constants/app_colors.dart';
import '../../../../translation/locale_keys.g.dart';
import '../../../../managers/theme_manager.dart';
import '../../widgets/general_item.dart';
import 'single_audio_screen.dart';

class AudiosTab extends StatefulWidget {
  const AudiosTab({
    super.key,
    required this.weekNo,
    required this.semesterNo,
    required this.subject,
  });

  final int weekNo;
  final int semesterNo;
  final WeekSubject subject;

  @override
  State<AudiosTab> createState() => _AudiosTabState();
}

class _AudiosTabState extends State<AudiosTab> {
  @override
  Widget build(BuildContext context) {
    // Filter items where itemType is audio from all lessons
    final audioItems = widget.subject.lessons
        .expand((lesson) => lesson.items)
        .where((item) => item.itemType == "audio")
        .toList();

    return Column(
      children: [
        if (audioItems.isEmpty)
          Center(
            child: Text(
              LocaleKeys.noAudioAvailable.tr(),
              style: ThemeManager.medium(
                size: 16,
                color: AppColors.black.withOpacity(0.7),
              ),
            ),
          )
        else
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColors.warmWhite,
              border: Border.all(
                color: AppColors.grey.withOpacity(0.5),
                width: 1,
              ),
            ),
            child: ListView.separated(
              shrinkWrap: true,
              itemBuilder: (context, index) => GeneralItem(
                onTabBackFunction: () {
                  PersistentNavBarNavigator.pushNewScreen(
                    context,
                    screen: SingleAudioScreen(
                      title: audioItems[index].title,
                      audio: audioItems[index].itemContent,
                    ),
                    withNavBar: true,
                    pageTransitionAnimation: PageTransitionAnimation.cupertino,
                  );
                },
                title: '${index + 1} - ${audioItems[index].title}',
                icon: Icons.audio_file_outlined,
              ),
              separatorBuilder: (context, index) => Container(
                color: AppColors.primary,
                height: 1,
                width: double.infinity,
              ),
              itemCount: audioItems.length,
            ),
          ),
      ],
    );
  }
}
