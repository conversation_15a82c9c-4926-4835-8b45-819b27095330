import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:provider/provider.dart';
import 'package:arepsalin/presentation_layer/course_feature/widgets/week_item.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../constants/app_colors.dart';
import '../../../data_layer/model/semester_model.dart';
import '../../../data_layer/model/week_model.dart';
import '../../../data_layer/model/week_response.dart';
import '../../../data_layer/provider/authentication_provider.dart';
import '../../../data_layer/service/semester_service.dart';
import '../../../managers/theme_manager.dart';
import '../../widgets/custom_progress_indicator.dart';
import '../screens/subject_screen.dart';

class SemesterItem extends StatefulWidget {
  const SemesterItem({
    super.key, 
    required this.semesterTitle, 
    required this.semesterNo,
    required this.isUnlocked,
    required this.semesterId,
  });

  final String semesterTitle;
  final int semesterNo;
  final bool isUnlocked;
  final int semesterId;

  @override
  State<SemesterItem> createState() => _SemesterItemState();
}

class _SemesterItemState extends State<SemesterItem> {
  bool isExpanded = false;
  List<WeekResponse> weeks = [];
  late AuthenticationProvider _authenticationProvider;

  bool isWeekOpen(WeekResponse week) {
    final now = DateTime.now();
    return week.startDate.isBefore(now) &&week.endDate.isAfter(now);
  }

  @override
  void initState() {
    super.initState();
    _authenticationProvider = context.read<AuthenticationProvider>();
  }

  Future<void> fetchWeeks() async {
    try {
      final semesterService = SemesterService();
      List<WeekResponse> fetchedWeeks = await semesterService.getSemesterWeeksById(widget.semesterId);

      setState(() {
        weeks = fetchedWeeks;
      });
    } catch (e) {
      debugPrint('Error fetching weeks: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 12, horizontal: 15),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppColors.warmWhite,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withOpacity(0.08),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () {
              if (widget.isUnlocked) {
                setState(() {
                  if (!isExpanded) {
                    fetchWeeks();
                  }
                  isExpanded = !isExpanded;
                });
              }
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.semesterTitle,
                      style: ThemeManager.semiBold(
                        size: 18,
                        color: widget.isUnlocked 
                          ? AppColors.black 
                          : AppColors.black.withOpacity(0.6),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    widget.isUnlocked
                      ? isExpanded
                        ? Icons.expand_less_rounded
                        : Icons.expand_more_rounded
                      : Icons.lock_rounded,
                    color: widget.isUnlocked 
                      ? AppColors.primary 
                      : AppColors.grey,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) ...[
            Container(
              height: 1,
              color: AppColors.borderGrey.withOpacity(0.5),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: weeks.isNotEmpty
                ? GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                      childAspectRatio: 1.2,
                    ),
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: weeks.length,
                    itemBuilder: (BuildContext context, int index) {
                      final week = weeks[index];
                      final isOpen = isWeekOpen(week);
                      print(isOpen);
                      return InkWell(
                        onTap: isOpen ? () {
                          PersistentNavBarNavigator.pushNewScreen(
                            context,
                            screen: SubjectScreen(
                              title: LocaleKeys.weekNumber
                                  .tr(args: ['${week.weekNo}']),
                              week: week,
                              semesterNo: widget.semesterNo,
                              subjectNames: const [],
                            ),
                            withNavBar: true,
                            pageTransitionAnimation:
                                PageTransitionAnimation.cupertino,
                          );
                        } : null,
                        child: WeekItem(
                          title: LocaleKeys.weekNumber
                              .tr(args: ['${week.weekNo}']),
                          isOpen: isOpen,
                        ),
                      );
                    },
                  )
                : const Center(
                    child: CustomProgressIndicator(),
                  ),
            ),
          ],
        ],
      ),
    );
  }
}
