import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import '../model/quiz_model.dart';

class NewQuizProvider extends ChangeNotifier {
  Quiz? quiz;
  int currentQuestionIndex = 0;
  Map<int, dynamic> userAnswers = {};

  // Audio file for record quizzes
  File? _audioFile;
  String? _audioFilePath;

  int remainingMinutes = 0;
  int remainingSeconds = 0;
  Duration? _quizDuration;
  Timer? _timer;
  bool _isQuizCompleted = false;

  bool get isQuizCompleted => _isQuizCompleted;

  void setQuiz(Quiz newQuiz) {
    quiz = newQuiz;
    currentQuestionIndex = 0;
    userAnswers = {};
    _isQuizCompleted = false;

    // Reset audio file for new quiz
    _audioFile = null;
    _audioFilePath = null;

    // Only set timer if timeLimit is provided and not a record quiz
    if (newQuiz.timeLimit != null && !newQuiz.isRecord) {
      _quizDuration = Duration(minutes: newQuiz.timeLimit!);
      remainingMinutes = newQuiz.timeLimit!;
      remainingSeconds = 0;
      startCountdownTimer();
    } else {
      // No timer for record quizzes or quizzes without timeLimit
      _quizDuration = null;
      remainingMinutes = 0;
      remainingSeconds = 0;
    }

    notifyListeners();
  }

  void startCountdownTimer() {
    if (_quizDuration == null) return; // Don't start timer if no duration

    _timer?.cancel();
    Duration duration = _quizDuration!;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (duration.inSeconds > 0) {
        duration = duration - const Duration(seconds: 1);
        remainingMinutes = duration.inMinutes;
        remainingSeconds = duration.inSeconds % 60;
        notifyListeners();
      } else {
        // Quiz time is up
        _timer?.cancel();
        _isQuizCompleted = true;
        notifyListeners();
      }
    });
  }

  void disposeTimer() {
    _timer?.cancel();
  }

  void updateMCQAnswer(int answerId) {
    if (quiz == null || quiz!.content.isEmpty) return;

    userAnswers[currentQuestionIndex] = answerId;
    notifyListeners();
  }

  void updateTextAnswer(String answer) {
    if (quiz == null || quiz!.content.isEmpty) return;

    userAnswers[currentQuestionIndex] = answer;
    notifyListeners();
  }

  // Audio file methods for record quizzes
  void setAudioFile(String filePath) {
    _audioFilePath = filePath;
    _audioFile = File(filePath);
    notifyListeners();
  }

  String? get audioFilePath => _audioFilePath;
  File? get audioFile => _audioFile;

  bool hasAudioFile() {
    return _audioFilePath != null && _audioFile != null;
  }

  void clearAudioFile() {
    _audioFile = null;
    _audioFilePath = null;
    notifyListeners();
  }

  void goToNextQuestion() {
    if (quiz == null || quiz!.content.isEmpty) return;

    if (currentQuestionIndex < quiz!.content.length - 1) {
      currentQuestionIndex++;
      notifyListeners();
    } else {
      _isQuizCompleted = true;
      notifyListeners();
    }
  }

  void goToPreviousQuestion() {
    if (quiz == null || quiz!.content.isEmpty) return;

    if (currentQuestionIndex > 0) {
      currentQuestionIndex--;
      notifyListeners();
    }
  }

  bool isQuizFinished() {
    if (quiz == null || quiz!.content.isEmpty) return true;
    return currentQuestionIndex == quiz!.content.length - 1;
  }

  bool hasAnsweredCurrentQuestion() {
    // For record quizzes, check if audio file is provided
    if (quiz?.isRecord == true) {
      return hasAudioFile();
    }
    return userAnswers.containsKey(currentQuestionIndex);
  }

  dynamic getCurrentAnswer() {
    return userAnswers[currentQuestionIndex];
  }

  int calculateScore() {
    if (quiz == null) return 0;

    int score = 0;
    for (int i = 0; i < quiz!.content.length; i++) {
      final question = quiz!.content[i];
      final userAnswer = userAnswers[i];

      if (userAnswer == null) continue;

      if (question.isMCQ() && userAnswer == question.correctAnswerId) {
        score += question.grade;
      } else if (question.isText() &&
          userAnswer.toString().trim().toLowerCase() ==
              question.correctAnswer?.trim().toLowerCase()) {
        score += question.grade;
      }
    }
    return score;
  }

  List<Map<String, dynamic>> getAnswersForSubmission() {
    List<Map<String, dynamic>> answers = [];

    // For record quizzes, return audio file path
    if (quiz?.isRecord == true) {
      if (hasAudioFile()) {
        answers.add({
          'type': 'audio',
          'audioFilePath': _audioFilePath,
          'quizId': quiz!.id,
          'grade': quiz!.grade,
        });
      }
      return answers;
    }

    // For regular quizzes
    userAnswers.forEach((index, answer) {
      final question = quiz!.content[index];

      if (question.isMCQ()) {
        // Find the selected answer text
        String userAnswerText = "";
        if (question.answers != null) {
          for (var ans in question.answers!) {
            if (ans.id == answer) {
              userAnswerText = ans.text;
              break;
            }
          }
        }

        answers.add({
          'type': 'mcq',
          'questionId': question.questionId,
          'question': question.question,
          'questionGrade': question.grade,
          'selectedAnswerId': answer,
          'userAnswer': userAnswerText,
        });
      } else if (question.isText()) {
        answers.add({
          'type': 'text',
          'questionId': question.questionId,
          'question': question.question,
          'questionGrade': question.grade,
          'userAnswer': answer,
        });
      }
    });

    return answers;
  }

  int getTimeTaken() {
    if (quiz?.timeLimit == null) return 0; // Return 0 if no time limit

    final totalSeconds = quiz!.timeLimit! * 60;
    final remainingSeconds = (remainingMinutes * 60) + this.remainingSeconds;
    return totalSeconds - remainingSeconds;
  }

  // Check if the quiz has a timer
  bool hasTimer() {
    return quiz?.timeLimit != null && quiz?.isRecord == false;
  }

  // Check if this is a record quiz
  bool isRecordQuiz() {
    return quiz?.isRecord == true;
  }

  @override
  void dispose() {
    disposeTimer();
    super.dispose();
  }
}
