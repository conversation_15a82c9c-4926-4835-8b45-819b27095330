import 'lesson_model.dart';

class WeekSubject {
  final int id;
  final String name;
  final String code;
  final List<Lesson> lessons;

  WeekSubject({
    required this.id,
    required this.name,
    required this.code,
    required this.lessons,
  });

  factory WeekSubject.fromJson(Map<String, dynamic> json) {
    return WeekSubject(
      id: json['id'],
      name: json['name'],
      code: json['code'],
      lessons: (json['lessons'] as List)
          .map((lesson) => Lesson.fromJson(lesson))
          .toList(),
    );
  }
}