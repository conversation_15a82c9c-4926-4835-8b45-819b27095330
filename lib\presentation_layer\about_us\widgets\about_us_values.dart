import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';
import '../../../translation/locale_keys.g.dart';

class AboutUsValues extends StatelessWidget {
  const AboutUsValues({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.symmetric(vertical: 12),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Icon(
                Icons.ac_unit_outlined,
                color: AppColors.bermuda,
                size: 20,
              ),
              const SizedBox(
                width: 4,
              ),
              Text(
                LocaleKeys.values.tr(),
                style: ThemeManager.bold(
                  color: AppColors.bermuda,
                  size: 22,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                width: 4,
              ),
              const Icon(
                Icons.ac_unit_outlined,
                color: AppColors.bermuda,
                size: 20,
              ),
            ],
          ),
          const SizedBox(height: 5),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Padding(
                padding: const EdgeInsetsDirectional.all(5),
                child: Text(
                  '1- ${LocaleKeys.loveFirstly.tr()}',
                  style:
                      ThemeManager.semiBold(color: AppColors.black, size: 18),
                  textAlign: TextAlign.center,
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.all(5),
                child: Text(
                  '2- ${LocaleKeys.workTogether.tr()}',
                  style:
                      ThemeManager.semiBold(color: AppColors.black, size: 18),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Padding(
                padding: const EdgeInsetsDirectional.all(5),
                child: Text(
                  '3- ${LocaleKeys.churchAffiliation.tr()}',
                  style:
                      ThemeManager.semiBold(color: AppColors.black, size: 18),
                  textAlign: TextAlign.center,
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.all(5),
                child: Text(
                  '4- ${LocaleKeys.accountability.tr()}',
                  style:
                      ThemeManager.semiBold(color: AppColors.black, size: 18),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsetsDirectional.all(5),
            child: Text(
              '5- ${LocaleKeys.transparency.tr()}',
              style: ThemeManager.semiBold(color: AppColors.black, size: 18),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
