class QuestionData {
  QuestionData(
      {required this.question,
        required this.answers,
        required this.grade,
        required this.type,
        required this.correctAnswer});

  String question;
  int grade;
  String type;
  List<String>? answers;
  int correctAnswer;

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'grade': grade,
      'type': type,
      'answers': answers,
      'correctAnswer':correctAnswer
    };
  }

  factory QuestionData.fromJson(Map<String, dynamic> json) {
    return QuestionData(
        question: json['question'],
        answers: List<String>.from(json['answers']),
        type: json['type'],
        grade: json['grade'],
        correctAnswer: json['correctAnswer']);
  }
}
