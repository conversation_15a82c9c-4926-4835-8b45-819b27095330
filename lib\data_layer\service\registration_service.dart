import 'dart:convert';

import 'package:arepsalin/data_layer/model/registration_model.dart';
import 'package:arepsalin/data_layer/service/file_upload_service.dart';
import 'package:arepsalin/presentation_layer/auth_feature/screens/login_screen.dart';
import 'package:arepsalin/presentation_layer/widgets/snackbar_widget.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';

import '../../constants/app_colors.dart';

class RegistrationService {
  final FileUploadService _fileUploadService = FileUploadService();

  // Base URL for the API
  final String baseUrl = 'https://aripsalin-api-production.up.railway.app';

  // Register a new user
  Future<bool> register({
    required BuildContext context,
    required RegistrationModel registrationData,
  }) async {
    try {
      // Show loading message
      SnackBarWidget(
        message: LocaleKeys.plzWait.tr(),
        context: context,
        backgroundColor: AppColors.black,
        messageColor: AppColors.white,
        messageSize: 16,
      ).showCustomSnackBar();

      // Upload files first
      final uploadResults = await _fileUploadService.uploadMultipleFiles(
        profilePicture: registrationData.profileImageFile,
        personalIDFront: registrationData.personalIDFrontFile,
        personalIDBack: registrationData.personalIDBackFile,
        tazkia: registrationData.tazkiaFile,
      );

      // Update registration data with file URLs
      registrationData.profilePicture = uploadResults['profilePicture'];
      registrationData.personalIDFront = uploadResults['personalIDFront'];
      registrationData.personalIDBack = uploadResults['personalIDBack'];
      registrationData.tazkia = uploadResults['tazkia'];
      print( jsonEncode(registrationData.toJson()));
      // Register user
      final response = await http.post(
        Uri.parse('$baseUrl/auth/register'),
        headers: <String, String>{
          'Content-Type': 'application/json',
        },
        body: jsonEncode(registrationData.toJson()),
      );

      if (response.statusCode == 201) {
        // Registration successful
        if (context.mounted) {
          SnackBarWidget(
            message: "Registration successful! Please login.",
            context: context,
            backgroundColor: AppColors.black,
            messageColor: AppColors.white,
            messageSize: 16,
          ).showCustomSnackBar();

          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: const LoginScreen(),
            withNavBar: false,
            pageTransitionAnimation: PageTransitionAnimation.cupertino,
          );
          return true;
        }
      } else if (response.statusCode == 400) {
        // Bad request - likely email already exists
        if (context.mounted) {
          SnackBarWidget(
            message: LocaleKeys.userExists.tr(),
            context: context,
            backgroundColor: AppColors.red,
            messageColor: AppColors.white,
            messageSize: 20,
          ).showCustomSnackBar();
        }
      } else {
        // Other error
        if (context.mounted) {
          SnackBarWidget(
            message: LocaleKeys.somethingWentWrong.tr(),
            context: context,
            backgroundColor: AppColors.red,
            messageColor: AppColors.white,
            messageSize: 16,
          ).showCustomSnackBar();
        }
      }
    } catch (e) {
      debugPrint('Error during registration: $e');
      if (context.mounted) {
        SnackBarWidget(
          message: LocaleKeys.somethingWentWrong.tr(),
          context: context,
          backgroundColor: AppColors.red,
          messageColor: AppColors.white,
          messageSize: 16,
        ).showCustomSnackBar();
      }
    }

    return false;
  }
}
