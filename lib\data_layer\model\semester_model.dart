import 'package:arepsalin/data_layer/model/week_model.dart';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../../managers/secure_storage.dart';

class Semester {
  final String id;
  final int semesterNo;
  final List<String> subjectNames;

  Semester({
    required this.id,
    required this.semesterNo,
    required this.subjectNames,
  });

  factory Semester.fromJson(Map<String, dynamic> json) {
    List<String> subjectNames = [];

    if (json['subjectNames'] != null) {
      var subjectNamesList = json['subjectNames'] as List<dynamic>;
      subjectNames =
          subjectNamesList.map((element) => element.toString()).toList();
    }

    return Semester(
      id: json['id'].toString(),
      semesterNo: json['semesterNo'],
      subjectNames: subjectNames,
    );
  }

  Future<List<Week>> fetchWeeks() async {
    try {
      final storage = SecureStorage();
      final token = await storage.getToken("access_token");

      final response = await http.get(
        Uri.parse(
            'https://aripsalin-api-production.up.railway.app/semester/$id/weeks'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> weeksData = jsonDecode(response.body);
        return weeksData.map((json) => Week.fromJson(json)).toList();
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching weeks: $e');
      return [];
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'semesterNo': semesterNo,
      'subjectNames': subjectNames,
    };
  }
}
