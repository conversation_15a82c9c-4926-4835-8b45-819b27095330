import 'package:flutter_test/flutter_test.dart';
import 'package:arepsalin/data_layer/model/quiz_model.dart';
import 'package:arepsalin/data_layer/provider/new_quiz_provider.dart';

void main() {
  group('Quiz Screen Tests', () {
    test('should handle empty content for record quiz', () {
      // Arrange
      final provider = NewQuizProvider();
      final recordQuiz = Quiz(
        id: 1,
        name: 'Record Quiz',
        grade: 50,
        type: 'record',
        numberOfAttempts: 1,
        timeLimit: null,
        subject: QuizSubject(id: 1, name: 'Arabic', code: 'AR101'),
        lesson: QuizLesson(id: 1, name: 'Recitation'),
        content: [], // Empty content for record quiz
        isRecord: true,
      );

      // Act
      provider.setQuiz(recordQuiz);

      // Assert
      expect(provider.quiz, isNotNull);
      expect(provider.quiz!.isRecord, isTrue);
      expect(provider.quiz!.content.isEmpty, isTrue);
      expect(provider.hasTimer(), isFalse);
      expect(provider.isRecordQuiz(), isTrue);
      expect(provider.isQuizFinished(), isTrue); // Should return true for empty content
    });

    test('should handle empty content for written quiz', () {
      // Arrange
      final provider = NewQuizProvider();
      final writtenQuiz = Quiz(
        id: 2,
        name: 'Written Quiz',
        grade: 100,
        type: 'regular',
        numberOfAttempts: 3,
        timeLimit: 30,
        subject: QuizSubject(id: 1, name: 'Math', code: 'MATH101'),
        lesson: QuizLesson(id: 1, name: 'Algebra'),
        content: [], // Empty content (error case)
        isRecord: false,
      );

      // Act
      provider.setQuiz(writtenQuiz);

      // Assert
      expect(provider.quiz, isNotNull);
      expect(provider.quiz!.isRecord, isFalse);
      expect(provider.quiz!.content.isEmpty, isTrue);
      expect(provider.hasTimer(), isFalse); // No timer for empty quiz
      expect(provider.isRecordQuiz(), isFalse);
      expect(provider.isQuizFinished(), isTrue); // Should return true for empty content
    });

    test('should handle normal quiz with content', () {
      // Arrange
      final provider = NewQuizProvider();
      final normalQuiz = Quiz(
        id: 3,
        name: 'Normal Quiz',
        grade: 100,
        type: 'regular',
        numberOfAttempts: 3,
        timeLimit: 45,
        subject: QuizSubject(id: 1, name: 'Math', code: 'MATH101'),
        lesson: QuizLesson(id: 1, name: 'Algebra'),
        content: [
          QuizQuestion(
            questionId: 1,
            question: 'What is 2+2?',
            type: 'mcq',
            grade: 10,
            answers: [
              QuizAnswer(id: 1, text: '3'),
              QuizAnswer(id: 2, text: '4'),
            ],
            correctAnswerId: 2,
          ),
        ],
        isRecord: false,
      );

      // Act
      provider.setQuiz(normalQuiz);

      // Assert
      expect(provider.quiz, isNotNull);
      expect(provider.quiz!.isRecord, isFalse);
      expect(provider.quiz!.content.isNotEmpty, isTrue);
      expect(provider.hasTimer(), isTrue); // Should have timer
      expect(provider.isRecordQuiz(), isFalse);
      expect(provider.isQuizFinished(), isFalse); // Should not be finished initially
    });

    test('should safely handle navigation methods with empty content', () {
      // Arrange
      final provider = NewQuizProvider();
      final emptyQuiz = Quiz(
        id: 4,
        name: 'Empty Quiz',
        grade: 50,
        type: 'regular',
        numberOfAttempts: 1,
        timeLimit: null,
        subject: QuizSubject(id: 1, name: 'Test', code: 'TEST'),
        lesson: QuizLesson(id: 1, name: 'Test Lesson'),
        content: [], // Empty content
        isRecord: false,
      );

      provider.setQuiz(emptyQuiz);

      // Act & Assert - These should not throw errors
      expect(() => provider.goToNextQuestion(), returnsNormally);
      expect(() => provider.goToPreviousQuestion(), returnsNormally);
      expect(() => provider.updateMCQAnswer(1), returnsNormally);
      expect(() => provider.updateTextAnswer('test'), returnsNormally);
      
      // Current question index should remain 0
      expect(provider.currentQuestionIndex, equals(0));
    });
  });
}
