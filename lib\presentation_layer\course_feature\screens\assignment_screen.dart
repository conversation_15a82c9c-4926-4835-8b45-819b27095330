import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as Path;
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:arepsalin/presentation_layer/home_feature/screens/initial_screen.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_images.dart';
import '../../../managers/theme_manager.dart';
import '../../widgets/custom_button.dart';
import '../widgets/audio_player.dart';
import '../widgets/audio_recorder.dart';

class AssignmentScreen extends StatefulWidget {
  const AssignmentScreen({super.key});

  @override
  _AssignmentScreenState createState() => _AssignmentScreenState();
}

class _AssignmentScreenState extends State<AssignmentScreen> {
  bool showPlayer = false;
  String? audioPath;

  @override
  void initState() {
    showPlayer = false;
    super.initState();
  }

  final FirebaseStorage storage = FirebaseStorage.instance;

  Future<void> uploadAudioFile(String filePath) async {
    try {
      String fileName =
          Path.basename(filePath); // Get the file name from the file path
      Reference ref = FirebaseStorage.instance
          .ref()
          .child('audio/$fileName'); // Set the storage path for the audio file
      File file = File(filePath);
      UploadTask uploadTask =
          ref.putFile(file); // Upload the file to Firebase Storage

      // Monitor the upload task
      TaskSnapshot snapshot = await uploadTask;
      if (snapshot.state == TaskState.success) {
        String downloadUrl = await ref
            .getDownloadURL(); // Get the download URL of the uploaded file
        print('File uploaded successfully. Download URL: $downloadUrl');
      } else {
        print('File upload failed');
      }
    } catch (e) {
      print('Error uploading file: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        centerTitle: true,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        title: const Text('التسميع'),
        titleTextStyle:
            ThemeManager.semiBold(size: 20, color: AppColors.warmWhite),
      ),
      body: Stack(
        children: [
          SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: Image(
                image: AssetImage(AppImages.backgroundImage),
                fit: BoxFit.fill,
              )),
          Container(
            //height: MediaQuery.sizeOf(context).height-80,
            color: AppColors.warmWhite.withOpacity(0.5),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 15.0, vertical: 10),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Container(
                          constraints: BoxConstraints(
                              maxHeight:
                                  MediaQuery.sizeOf(context).height / 1.9),
                          decoration: BoxDecoration(
                              color: AppColors.warmWhite,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: AppColors.actionsColor.withOpacity(0.7),
                              )),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 10),
                          child: SingleChildScrollView(
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      'الميعاد النهائى :',
                                      textAlign: TextAlign.right,
                                      style: ThemeManager.bold(
                                          size: 18, color: AppColors.black),
                                    ),
                                    const SizedBox(
                                      width: 5,
                                    ),
                                    Text(
                                      '12-12-2023',
                                      textAlign: TextAlign.right,
                                      style: ThemeManager.medium(
                                          size: 16,
                                          color: AppColors.actionsColor),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                Row(
                                  children: [
                                    Text(
                                      'المطلوب :',
                                      textAlign: TextAlign.right,
                                      style: ThemeManager.bold(
                                          size: 18, color: AppColors.black),
                                    ),
                                    const SizedBox(
                                      width: 5,
                                    ),
                                    Text(
                                      'تسميع لحن اربيصالين',
                                      textAlign: TextAlign.right,
                                      style: ThemeManager.medium(
                                          size: 16,
                                          color: AppColors.actionsColor),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                Row(
                                  children: [
                                    Text(
                                      'ملحوظة :',
                                      textAlign: TextAlign.right,
                                      style: ThemeManager.bold(
                                          size: 18, color: AppColors.black),
                                    ),
                                    const SizedBox(
                                      width: 5,
                                    ),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'jj',
                                            textAlign: TextAlign.right,
                                            style: ThemeManager.medium(
                                                size: 16,
                                                color: AppColors.actionsColor),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 15,
                        ),
                        Container(
                          margin: const EdgeInsets.symmetric(
                              horizontal: 0, vertical: 35),
                          child: showPlayer
                              ? CustomAudioPlayer(
                                  source: audioPath!,
                                  onDelete: () {
                                    setState(() => showPlayer = false);
                                  },
                                )
                              : AudioRecorderWidget(
                                  onStop: (path) {
                                    audioPath = path;
                                    if (kDebugMode) {
                                      print('Recorded file path: $path');
                                    }
                                    setState(() {
                                      audioPath = path;
                                      showPlayer = true;
                                    });
                                  },
                                ),
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 15.0, vertical: 10),
                  child: CustomButton(
                    textStyle:
                        ThemeManager.bold(color: AppColors.white, size: 18),
                    height: 55,
                    borderRadius: 10,
                    borderColor: AppColors.transparent,
                    btnColor: audioPath == null || showPlayer == false
                        ? AppColors.grey
                        : AppColors.primary,
                    title: LocaleKeys.submit.tr(),
                    onPressed: () {
                      if (showPlayer) {
                        PersistentNavBarNavigator.pushNewScreen(
                          context,
                          screen: const InitialScreen(),
                          withNavBar: true,
                          pageTransitionAnimation:
                              PageTransitionAnimation.cupertino,
                        );
                        uploadAudioFile(audioPath!);
                      }
                    },
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
