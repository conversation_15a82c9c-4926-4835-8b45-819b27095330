import 'dart:ui';

class Subject {
  final int id;
  final String name;
  final String code;

  Subject({
    required this.id,
    required this.name,
    required this.code,
  });

  factory Subject.fromJson(Map<String, dynamic> json) {
    return Subject(
      id: json['id'],
      name: json['name'],
      code: json['code'],
    );
  }
}

class WeekQuiz {
  final int id;
  final String name;
  final int? timeLimit; // Made optional
  final int grade;
  final int numberOfAttempts;
  final String type;
  final Subject? subject;
  final int userAttempts;
  final int? lessonId;
  final int? order;
  final String status;
  final bool isRecord; // Added isRecord field

  WeekQuiz({
    required this.id,
    required this.name,
    this.timeLimit, // Made optional
    required this.grade,
    required this.numberOfAttempts,
    required this.type,
    this.subject,
    this.userAttempts = 0,
    this.lessonId,
    this.order,
    this.status = 'open',
    this.isRecord = false, // Added isRecord field with default value
  });

  factory WeekQuiz.fromJson(Map<String, dynamic> json) {
    return WeekQuiz(
      id: json['id'],
      name: json['name'],
      timeLimit: json['timeLimit'], // Can be null now
      grade: json['grade'],
      numberOfAttempts: json['numberOfAttempts'],
      type: json['type'],
      subject: json['subject'] != null ? Subject.fromJson(json['subject']) : null,
      userAttempts: json['userAttempts'] ?? 0,
      lessonId: json['lessonId'],
      order: json['order'],
      status: json['status'] ?? 'open',
      isRecord: json['isRecord'] ?? false, // Default to false if not present
    );
  }

  bool get isFinal => type.toLowerCase() == 'final';

  bool get isAttemptLimitReached => userAttempts >= numberOfAttempts;

  int get remainingAttempts => numberOfAttempts - userAttempts;

  // New method to determine if a quiz is accessible
  bool get isAccessible {
    return status == 'open' ||
           status == 'redo' ||
           (status == 'fail' && userAttempts < numberOfAttempts);
  }

  // Get color based on status
  Color get statusColor {
    switch (status) {
      case 'pending':
        return const Color(0xFFFFA500); // Orange/Amber
      case 'pass':
        return const Color(0xFF4CAF50); // Green
      case 'fail':
        return const Color(0xFFF44336); // Red
      case 'excellent':
        return const Color(0xFFFFD700); // Gold
      case 'closed':
        return const Color(0xFF9E9E9E); // Gray
      case 'redo':
        return const Color(0xFF9C27B0); // Purple
      case 'open':
      default:
        return const Color(0xFF2196F3); // Blue
    }
  }

  // Check if the quiz has a timer
  bool get hasTimer => timeLimit != null && !isRecord;

  // Check if this is a record quiz
  bool get isRecordQuiz => isRecord;
}
