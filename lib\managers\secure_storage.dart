import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:jwt_decoder/jwt_decoder.dart';

class SecureStorage {
  final FlutterSecureStorage _storage = FlutterSecureStorage();


  Future<void> saveToken(String key, String token) async {
    await _storage.write(key: key, value: token);
  }


  Future<String?> getToken(String key) async {
    return await _storage.read(key: key);
  }

  Future<void> deleteToken(String key) async {
    await _storage.delete(key: key);
  }
  static Future<void> checkTokenExpired()async {
    SecureStorage s =  SecureStorage();

    var refreshToken = await s.getToken('refresh_token');
    var accessToken = await s.getToken('access_token');

    Map<String, dynamic> decodedToken = JwtDecoder.decode(accessToken!);

    int? expiryTimeInSeconds = decodedToken['exp'];

    if (expiryTimeInSeconds == null) {
         print("expiry time IS NULL");
      return;
    }

    DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryTimeInSeconds * 1000);

    DateTime currentDate = DateTime.now();
    print("expiiiiirrryyy Daaateee ${expiryDate}  ${currentDate} : ${currentDate.isAfter(expiryDate)}");
    if(currentDate.isAfter(expiryDate)){

      final response = await http.post(
        Uri.parse('https://aripsalin-api-production.up.railway.app/auth/refresh'),
        headers: <String, String>{
          'Content-Type': 'application/json',
          'Authorization':'Bearer $refreshToken',
        },
      );
      if (response.statusCode == 200) {
      print("gagedt el access tokeeeeeeeeeeen");
        final jsonResponse = jsonDecode(response.body);
        final accessToken = jsonResponse['access_token'];
        final refreshToken = jsonResponse['refresh_token'];
        await s.saveToken('access_token', accessToken);
        await s.saveToken('refresh_token', refreshToken);

      }else{
        print(response.body);
        print(response.statusCode);
      }
    }
    // Compare current date with expiration date
    return ; // Returns true if expired, false otherwise
  }
}

// final credential = await FirebaseAuth.instance
//     .signInWithEmailAndPassword(email: email, password: pass);
//
// final userData = {
//   'email': email,
//   'uid': credential.user!.uid,
// };
// await UserCacheManager.cacheUserData(userData);

