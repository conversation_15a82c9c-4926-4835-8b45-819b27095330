import 'package:flutter/material.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';

class ExpandableItem extends StatefulWidget {
  const ExpandableItem(
      {Key? key, required this.title, required this.icon, required this.onTap})
      : super(key: key);
  final String title;

  final IconData icon;

  final Function() onTap;

  @override
  State<ExpandableItem> createState() => _ExpandableItemState();
}

class _ExpandableItemState extends State<ExpandableItem> {
  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.sizeOf(context);
    return InkWell(
      onTap: () {
        widget.onTap;
      },
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 30),
        padding: const EdgeInsets.symmetric(horizontal: 12),
        height: size.height / 12.5,
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
                color: AppColors.black.withOpacity(0.5),
                blurRadius: 1,
                spreadRadius: 0.2)
          ],
          borderRadius: BorderRadius.circular(15),
          color: AppColors.darkGrey,
        ),
        child: Row(
          children: [
            Icon(widget.icon),
            const SizedBox(
              width: 5,
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  height: 5,
                ),
                Text(
                  widget.title,
                  style:
                      ThemeManager.semiBold(size: 20, color: AppColors.black),
                ),
              ],
            ),
            const Spacer(),
            Icon(
              widget.icon,
              size: 30,
            )
          ],
        ),
      ),
    );
  }
}
