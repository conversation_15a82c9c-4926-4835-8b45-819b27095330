import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

class ImageInput extends StatefulWidget {
  const ImageInput({super.key, required this.onPickImage});

  final void Function(File image) onPickImage;

  @override
  State<ImageInput> createState() => _ImageInputState();
}

class _ImageInputState extends State<ImageInput> {
  File? _selectedImage;

  void _takePic() async {
    final imagePicker = ImagePicker();
    final pickedImage =
        await imagePicker.pickImage(source: ImageSource.gallery, maxWidth: 600);

    if (pickedImage == null) {
      return;
    }
    setState(() {
      _selectedImage = File(pickedImage.path);
    });
    widget.onPickImage(_selectedImage!);
  }

  @override
  Widget build(BuildContext context) {
    Widget content = TextButton.icon(
        style: ButtonStyle(
            iconColor:
                WidgetStatePropertyAll(Theme.of(context).colorScheme.onSurface),
            textStyle: WidgetStatePropertyAll(TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
            ))),
        onPressed: _takePic,
        icon: const Icon(
          Icons.camera,
          size: 25,
        ),
        label: Text(
          LocaleKeys.takePic.tr(),
          style: ThemeManager.semiBold(size: 16),
        ));

    if (_selectedImage != null) {
      content = GestureDetector(
        onTap: _takePic,
        child: Image.file(
          _selectedImage!,
          fit: BoxFit.cover,
          width: 250,
          height: double.infinity,
        ),
      );
    }
    return Container(
        width: 250,
        height: 250,
        decoration: BoxDecoration(
            border: Border.all(
          width: 2.5,
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        )),
        child: content);
  }
}
