import 'package:flutter/material.dart';

import '../../constants/app_colors.dart';
import '../../managers/theme_manager.dart';

class SnackBarWidget {
  final String message;
  final double? messageSize;
  final BuildContext context;
  final bool? isBottomPadding;
  final bool? withErrorIcon;
  final Widget? tail;
  final Color? backgroundColor;
  final Color? messageColor;

  const SnackBarWidget(
      {required this.context,
      required this.message,
      required this.backgroundColor,
      required this.messageColor,
      required this.messageSize,
      this.withErrorIcon,
      this.isBottomPadding = false,
      this.tail});

  showCustomSnackBar() {
    ScaffoldMessenger.of(context).removeCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        duration: const Duration(seconds: 5),
        padding: isBottomPadding != null && isBottomPadding == true
            ? const EdgeInsetsDirectional.only(
                bottom: kBottomNavigationBarHeight + 10)
            : null,
        content: Wrap(
          crossAxisAlignment: WrapCrossAlignment.center,
          alignment: WrapAlignment.start,
          children: [
            withErrorIcon == null || withErrorIcon == true
                ? const Padding(
                    padding: EdgeInsetsDirectional.only(end: 8),
                    child: Icon(Icons.error_outline, color: AppColors.white),
                  )
                : const SizedBox(height: 30),
            Wrap(
              children: [
                const SizedBox(width: 4),
                Text(
                  message,
                  textAlign: TextAlign.start,
                  style: ThemeManager.medium(
                      size: messageSize, color: messageColor),
                ),
                withErrorIcon == false && tail != null
                    ? tail!
                    : const SizedBox.shrink()
              ],
            )
          ],
        ),
        backgroundColor: backgroundColor,
      ),
    );
  }
}
