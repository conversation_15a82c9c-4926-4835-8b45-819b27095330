import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../constants/app_colors.dart';
import '../../managers/theme_manager.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Icon? icon;
  final double? elevation;
  final bool centerTitle;
  final List<Widget>? actions;
  final bool backIcon;
  final TextStyle? titleStyle;
  final double? actionsPadding;
  final Color? backColor;

  const CustomAppBar(
      {Key? key,
      this.actionsPadding,
      this.title,
      this.backColor,
      this.icon,
      this.elevation,
      this.centerTitle = true,
      this.actions,
      this.backIcon = true,
      this.titleStyle})
      : super(key: key);

  @override
  Size get preferredSize => const Size(double.infinity, kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      scrolledUnderElevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle.dark.copyWith(
        statusBarColor: backColor ?? AppColors.white,
      ),
      elevation: elevation ?? 0,
      backgroundColor: backColor ?? AppColors.white,
      leading: icon == null && backIcon == false
          ? null
          : Padding(
              padding: const EdgeInsets.all(5),
              child: IconButton(
                icon: icon ??
                    const Icon(
                      Icons.arrow_back_ios,
                      color: AppColors.actionsColor,
                      size: 16,
                    ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ),
      actions: actions != null
          ? actions!
              .map((action) => Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: actionsPadding ?? 0),
                    child: action,
                  ))
              .toList()
          : [],
      title: title != null
          ? Text(title!,
              style: titleStyle ??
                  ThemeManager.semiBold(size: 17, color: AppColors.primary))
          : const SizedBox.shrink(),
      centerTitle: title != null && centerTitle ? true : false,
    );
  }
}
