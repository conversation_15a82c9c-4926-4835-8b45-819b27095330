import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/managers/theme_manager.dart';

class PdfScreen extends StatelessWidget {
  const PdfScreen({Key? key, required this.title, required this.pdf})
      : super(key: key);
  final String title;

  final String pdf;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: AppColors.primary,
            statusBarIconBrightness: Brightness.light,
            statusBarBrightness: Brightness.light,
          ),
          centerTitle: true,
          foregroundColor: AppColors.white,
          backgroundColor: AppColors.primary,
          title: Text(title),
          titleTextStyle:
              ThemeManager.semiBold(size: 20, color: AppColors.warmWhite),
        ),
        body: Container(
            child: SfPdfViewer.network(pdf,
                canShowScrollHead: false, canShowScrollStatus: false)));
  }
}
