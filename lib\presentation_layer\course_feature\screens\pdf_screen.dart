import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:no_screenshot/no_screenshot.dart';

class PdfScreen extends StatefulWidget {
  const PdfScreen({Key? key, required this.title, required this.pdf})
      : super(key: key);
  final String title;
  final String pdf;

  @override
  State<PdfScreen> createState() => _PdfScreenState();
}

class _PdfScreenState extends State<PdfScreen> {
  final _noScreenshot = NoScreenshot.instance;

  @override
  void initState() {
    super.initState();
    _enableScreenshotProtection();
  }

  @override
  void dispose() {
    _disableScreenshotProtection();
    super.dispose();
  }

  Future<void> _enableScreenshotProtection() async {
    try {
      await _noScreenshot.screenshotOff();
      debugPrint('Screenshot protection enabled');
    } catch (e) {
      debugPrint('Error enabling screenshot protection: $e');
    }
  }

  Future<void> _disableScreenshotProtection() async {
    try {
      await _noScreenshot.screenshotOn();
      debugPrint('Screenshot protection disabled');
    } catch (e) {
      debugPrint('Error disabling screenshot protection: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: AppColors.primary,
            statusBarIconBrightness: Brightness.light,
            statusBarBrightness: Brightness.light,
          ),
          centerTitle: true,
          foregroundColor: AppColors.white,
          backgroundColor: AppColors.primary,
          title: Text(widget.title),
          titleTextStyle:
              ThemeManager.semiBold(size: 20, color: AppColors.warmWhite),
        ),
        body: SfPdfViewer.network(
          widget.pdf,
          canShowScrollHead: false,
          canShowScrollStatus: false,
        ));
  }
}
