class User {
  final int id;
  final String email;
  final String name;
  final DateTime birthday;
  final String nationality;
  final String address;
  final String gender;
  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    required this.id,
    required this.email,
    required this.name,
    required this.birthday,
    required this.nationality,
    required this.address,
    required this.gender,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      name: json['name'],
      birthday: DateTime.parse(json['birthday']),
      nationality: json['nationality'],
      address: json['Address'],
      gender: json['gender'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'birthday': birthday.toIso8601String(),
      'nationality': nationality,
      'Address': address,
      'gender': gender,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}