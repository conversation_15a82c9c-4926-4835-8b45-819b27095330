import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_expanded_tile/flutter_expanded_tile.dart';
import 'package:arepsalin/presentation_layer/course_feature/widgets/year_grade.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';
import '../../../translation/locale_keys.g.dart';

class GradesScreen extends StatefulWidget {
  const GradesScreen({Key? key}) : super(key: key);

  @override
  State<GradesScreen> createState() => _GradesScreenState();
}

class _GradesScreenState extends State<GradesScreen> {
  List<String> yearsName = [
    LocaleKeys.firstYear.tr(),
    LocaleKeys.secondYear.tr(),
    LocaleKeys.thirdYear.tr(),
    LocaleKeys.fourthYear.tr(),
    LocaleKeys.fifthYear.tr(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.primary,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        centerTitle: true,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        title: Text(
          LocaleKeys.grades.tr(),
          style: ThemeManager.semiBold(size: 22, color: AppColors.white),
        ),
        titleTextStyle:
            ThemeManager.semiBold(size: 20, color: AppColors.warmWhite),
      ),
      body: Container(
        padding: const EdgeInsets.only(right: 16, left: 16, top: 16),
        color: AppColors.warmWhite,
        child: ExpandedTileList.builder(
          itemCount: 5,
          maxOpened: 1,
          reverse: false,
          itemBuilder: (context, index, controller) {
            return ExpandedTile(
              theme: ExpandedTileThemeData(
                headerColor: AppColors.grey.withOpacity(0.6),
                // headerRadius: 10.0,
                headerPadding: const EdgeInsets.all(15.0),
                headerSplashColor: AppColors.transparent,
                contentBackgroundColor: AppColors.darkGrey,
                contentPadding: const EdgeInsets.all(0),
                // contentPadding: 10.0,
              ),
              controller: controller,
              title: Text(
                yearsName[index],
                style: ThemeManager.bold(size: 16, color: AppColors.black),
              ),
              content: const YearGrade(),
              onTap: () {
                /// fun to get grade of year
              },
            );
          },
        ),
      ),
    );
  }
}
