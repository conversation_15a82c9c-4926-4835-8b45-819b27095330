import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

import '../constants/variables.dart';
import '../managers/shared_pref_manager.dart';
import 'model/respone_wraper.dart';

class ApiHelper {
  static final ApiHelper _singleton = ApiHelper._internal();

  factory ApiHelper() {
    return _singleton;
  }

  ApiHelper._internal();

  String? token = "";
  String langCode = "";

  Future<ResponseWrapper?> postRequest(
      {required String uri,
      required dynamic body,
      String? verTkn,
      String? userTkn}) async {
    await getTokenLang();
    // await getCurrentLocation();
    Map<String, String>? headers = {
      "Content-type": "application/json",
      "Accept": "application/json",
      "Timezone": DateTime.now().timeZoneOffset.toString(),
      "Authorization": userTkn != null ? "Bearer $userTkn" : "Bearer $token",
      "verification-token": "$verTkn",
      "Accept-Language": langCode,
    };
    http.Response response;
    try {
      response = await http.post(Uri.parse("${Variables.baseUrl}$uri"),
          headers: headers, body: body);
      log("${Variables.baseUrl}$uri", name: "*URL*");
      log(body, name: "*BODY*");
      debugPrint(
          "response.statusCode in post request : ${response.statusCode}");
      debugPrint("response.body in post request : ${response.body}");
      return extractData(response);
    } catch (err) {
      debugPrint('error of post request : $err');
      // err from server
      // Navigate to main
    }
    return null;
  }

  Future<ResponseWrapper?> putRequest(
      {required String uri, required String body}) async {
    await getTokenLang();
    Map<String, String> headers = {
      "Content-type": "application/json",
      "Accept": "application/json",
      "Authorization": "Bearer $token",
      "Accept-Language": langCode,
      "Timezone": DateTime.now().timeZoneOffset.toString(),
    };
    http.Response response;
    try {
      response = await http.put(Uri.parse("${Variables.baseUrl}$uri"),
          headers: headers, body: body);
      log("${Variables.baseUrl}$uri", name: "*URL*");
      log(body, name: "*BODY*");
      debugPrint("response.statusCode in put request : ${response.statusCode}");
      return extractData(response);
    } catch (err) {
      debugPrint('error of put request : $err');
      // err from server
      // Navigate to main
    }
    return null;
  }

  Future<ResponseWrapper?> patchRequest(
      {required String uri, required String body}) async {
    await getTokenLang();
    Map<String, String> headers = {
      "Content-type": "application/json",
      "Accept": "application/json",
      "Authorization": "Bearer $token",
      "Accept-Language": langCode,
      "Timezone": DateTime.now().timeZoneOffset.toString(),
    };
    http.Response response;
    try {
      response = await http.patch(Uri.parse("${Variables.baseUrl}$uri"),
          headers: headers, body: body);
      log("${Variables.baseUrl}$uri", name: "*URL*");
      log(body, name: "*BODY*");
      debugPrint("response.statusCode in put request : ${response.statusCode}");
      return extractData(response);
    } catch (err) {
      debugPrint('error of put request : $err');
    }
    return null;
  }

  Future<ResponseWrapper?> getRequest({required String uri}) async {
    await getTokenLang();
    Map<String, String> headers = {
      "Content-type": "application/json",
      "Accept": "application/json",
      "Authorization": "Bearer $token",
      "Accept-Language": langCode,
      "Timezone": DateTime.now().timeZoneOffset.toString(),
    };
    http.Response response;
    try {
      response = await http.get(Uri.parse("${Variables.baseUrl}$uri"),
          headers: headers);
      log("$headers", name: "*headers*");
      log("${Variables.baseUrl}$uri", name: "*URL*");
      debugPrint("response.statusCode in get request : ${response.statusCode}");
      return extractData(response);
    } catch (err) {
      debugPrint('error of get request : $err');
      // err from server
      // Navigate to main
    }
    return null;
  }

  Future<ResponseWrapper?> deleteRequest(
      {required String uri, Map<String, dynamic>? model}) async {
    await getTokenLang();
    Map<String, String> headers = {
      "Content-type": "application/json",
      "Authorization": "Bearer $token",
      "Accept-Language": langCode,
      "Timezone": DateTime.now().timeZoneOffset.toString(),
    };
    http.Response response;
    try {
      String? body = model != null ? _encodeAndPrint(model) : null;
      response = await http.delete(Uri.parse("${Variables.baseUrl}$uri"),
          headers: headers, body: body);
      log("${Variables.baseUrl}$uri", name: "*URL*");
      log(body.toString(), name: "*BODY*");
      debugPrint(
          "response.statusCode in delete request : ${response.statusCode}");
      return extractData(response);
    } catch (err) {
      debugPrint('error of delete request : $err');
      // err from server
      // Navigate to main
    }
    return null;
  }

  String _encodeAndPrint(Map<String, dynamic> map) {
    final body = json.encode(map);
    log(body, name: 'NETWORK_REQUEST');
    return body;
  }

  Future getTokenLang() async {
    SharedPrefManager sharedPrefManager = SharedPrefManager();
    token = await sharedPrefManager.read(Variables.accessToken);
    langCode = await sharedPrefManager.read(Variables.langCode);
    if (langCode.isEmpty) {
      langCode = Variables.enLangCode;
    }
  }

  Future<ResponseWrapper> extractData(http.Response response) {
    return Future.value(
      response.statusCode < 300
          ? ResponseWrapper(
              true,
              response.statusCode,
              response: _decodeAndPrint(response.body),
            )
          : ResponseWrapper(
              false,
              response.statusCode,
              error: _propagateError(response),
            ),
    );
  }

  dynamic _decodeAndPrint(String raw) {
    var response = json.decode(raw);
    log(response.toString(), name: 'NETWORK_RESPONSE');
    return response;
  }

  Error? _propagateError(http.Response response) {
    try {
      final Map<String, dynamic> errorMap = json.decode(response.body);
      log("${errorMap['error']}", name: "Error");
      log("${errorMap["message"]}", name: "message");
      if (errorMap['error'] is String) {
        return Error(error: errorMap['error'], message: errorMap['message']);
      } else if (errorMap["message"] != null) {
        return Error(error: errorMap['message'], message: errorMap['message']);
      }
      return Error(error: errorMap['error'], message: errorMap['message']);
    } catch (e) {
      log("--------------", name: "Error catch");
      log("RESPONSE REQUEST${response.request}");
      return Error(error: 'Parsing error', message: 'Parsing error');
    }
  }
}
