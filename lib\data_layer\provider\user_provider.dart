import 'dart:convert';

import 'package:arepsalin/constants/variables.dart';
import 'package:flutter/material.dart';

import 'package:http/http.dart' as http;

import '../../managers/secure_storage.dart';
import '../model/student_model.dart';
import '../model/user_model.dart';


class UserProvider with ChangeNotifier {
  Student? _student;
  bool _isLoading = false;
  final SecureStorage _secureStorage = SecureStorage();

  Student? get student => _student;
  bool get isLoading => _isLoading;

  // Fetch user data from API with stored token
  Future<void> fetchUser() async {
    _isLoading = true;
    notifyListeners();
    await SecureStorage.checkTokenExpired();
    String? accessToken = await _secureStorage.getToken("access_token");
    if (accessToken == null) {
      print("No access token found");
      _isLoading = false;
      notifyListeners();
      return;
    }

    final url = Uri.parse('${Variables.baseUrl}/user/info'); // Replace with your API URL

    try {
      final response = await http.get(
        url,
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $accessToken",
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _student = Student.fromJson(data);
        print("Student ID: ${_student?.id}");
      } else {
        throw Exception('Failed to load user data');
      }
    } catch (error) {
      print("Error fetching user: $error");
    }

    _isLoading = false;
    notifyListeners();
  }

  // Save the access token securely
  Future<void> saveToken(String token) async {
    await _secureStorage.saveToken("access_token", token);
    notifyListeners();
  }

  // Update user details with stored token
  Future<void> updateUser(Student updatedUser) async {
    _isLoading = true;
    notifyListeners();

    String? accessToken = await _secureStorage.getToken("access_token");
    if (accessToken == null) {
      print("No access token found");
      _isLoading = false;
      notifyListeners();
      return;
    }

    final url = Uri.parse('https://yourapi.com/users/${updatedUser.id}'); // Replace with your API URL

    try {
      final response = await http.put(
        url,
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $accessToken",
        },
        body: json.encode(updatedUser.toJson()),
      );

      if (response.statusCode == 200) {
        _student = updatedUser;
      } else {
        throw Exception('Failed to update user data');
      }
    } catch (error) {
      print("Error updating user: $error");
    }

    _isLoading = false;
    notifyListeners();
  }

  // Logout user (Clears user data & deletes stored token)
  Future<void> logout() async {
    _student = null;
    await _secureStorage.deleteToken("access_token");
    await _secureStorage.deleteToken("refresh_token");
    notifyListeners();
  }
}
