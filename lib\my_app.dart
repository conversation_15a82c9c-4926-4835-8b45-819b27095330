import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:arepsalin/managers/secure_storage.dart';
import 'package:arepsalin/presentation_layer/auth_feature/screens/login_screen.dart';
import 'package:arepsalin/presentation_layer/home_feature/screens/initial_screen.dart';

import 'constants/functions.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    return MultiProvider(
      providers: Functions.getProviders(),
      builder: (context, _) {
        return MaterialApp(
          localizationsDelegates: [...context.localizationDelegates],
          supportedLocales: context.supportedLocales,
          locale: context.locale,
          debugShowCheckedModeBanner: false,
          title: 'Tartil',
          theme: ThemeData(useMaterial3: true),
          home: FutureBuilder<String?>(
            future: SecureStorage().getToken('refresh_token'),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                // Return a blank container while checking auth status
                // The native splash screen will be shown during this time
                return Container(color: Colors.white);
              } else {
                // If we have a token, go to InitialScreen, otherwise LoginScreen
                return snapshot.data != null
                    ? const InitialScreen()
                    : const LoginScreen();
              }
            },
          ),
        );
      },
    );
  }
}
