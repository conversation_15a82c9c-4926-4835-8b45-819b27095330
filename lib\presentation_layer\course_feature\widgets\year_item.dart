import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import '../../../data_layer/model/enrolled_semester.dart';

import '../screens/year_detail_view_screen.dart';

class YearItem extends StatelessWidget {
  const YearItem({
    super.key,
    required this.title,
    required this.image,
    required this.isOpen,
    required this.semesterNo,
    required this.enrolledSemesters,
  });

  final String title;
  final String image;
  final bool isOpen;
  final int semesterNo;
  final List<EnrolledSemester> enrolledSemesters;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (isOpen) {
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: YearDetailViewScreen(
              year: title,
              semesterNo: semesterNo,
              enrolledSemesters: enrolledSemesters,
            ),
            withNavBar: true,
            pageTransitionAnimation: PageTransitionAnimation.cupertino,
          );
        }
      },
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 13),
        clipBehavior: Clip.hardEdge,
        height: MediaQuery.sizeOf(context).height / 5.8,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(10)),
        child: Stack(
          children: [
            Image(
              image: AssetImage(image),
              fit: BoxFit.cover,
              width: double.infinity,
            ),
            Container(
              height: MediaQuery.sizeOf(context).height / 5.5,
              width: double.infinity,
              color: AppColors.black.withOpacity(0.15),
            ),
            Center(
              child: Text(
                title,
                style: ThemeManager.bold(size: 24, color: AppColors.white),
                textAlign: TextAlign.center,
              ),
            ),
            !isOpen
                ? Positioned.directional(
                    textDirection: TextDirection.ltr,
                    top: 12,
                    start: 8,
                    child: const Icon(
                      Icons.lock,
                      color: AppColors.grey,
                      size: 35,
                    ),
                  )
                : const SizedBox.shrink()
          ],
        ),
      ),
    );
  }
}
