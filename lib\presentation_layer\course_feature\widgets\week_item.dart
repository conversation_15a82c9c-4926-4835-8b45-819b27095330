import 'package:flutter/material.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';

class WeekItem extends StatelessWidget {
  const WeekItem({
    Key? key, 
    required this.title, 
    required this.isOpen
  }) : super(key: key);
  
  final String title;
  final bool isOpen;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsetsDirectional.symmetric(horizontal: 4, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isOpen ? AppColors.primary : AppColors.primary.withOpacity(0.7),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
              child: Text(
                title,
                style: ThemeManager.bold(
                  size: 16,
                  color: isOpen ? AppColors.white : AppColors.white.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          if (!isOpen)
            Positioned(
              top: 8,
              right: 8,
              child: Icon(
                Icons.lock,
                size: 18,
                color: AppColors.white.withOpacity(0.7),
              ),
            ),
        ],
      ),
    );
  }
}
