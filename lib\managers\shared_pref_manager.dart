import 'package:shared_preferences/shared_preferences.dart';

class SharedPrefManager {
  static final SharedPrefManager _singleton = SharedPrefManager._internal();

  factory SharedPrefManager() {
    return _singleton;
  }

  SharedPrefManager._internal();

  Future<SharedPreferences> get _sharedPref async =>
      await SharedPreferences.getInstance();

  Future<String> read(String key) async {
    SharedPreferences sharedPref = await _sharedPref;
    var data = sharedPref.getString(key);
    return data ?? "";
  }

  void write(String key, String value) async {
    SharedPreferences sharedPref = await _sharedPref;
    sharedPref.setString(key, value);
  }

  Future<void> remove(String key) async {
    SharedPreferences sharedPref = await _sharedPref;
    sharedPref.remove(key);
  }

  void clear() async {
    SharedPreferences sharedPref = await _sharedPref;
    sharedPref.clear();
  }
}
