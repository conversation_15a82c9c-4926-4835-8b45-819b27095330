import 'package:flutter/material.dart';
import 'package:arepsalin/constants/app_colors.dart';

import '../../../managers/theme_manager.dart';
import '../../widgets/custom_image.dart';

class HomeItem extends StatelessWidget {
  const HomeItem(
      {super.key,
      required this.image,
      required this.title,
      required this.onPressed});

  final String image;
  final String title;
  final void Function() onPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsetsDirectional.symmetric(vertical: 15),
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border.all(color: AppColors.primary),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.5),
            offset: const Offset(5, 5), // Adjust the offset
            blurRadius: 5, // Adjust the blur radius
            spreadRadius: 0.1, // Adjust the spread radius
          )
        ],
      ),
      child: In<PERSON><PERSON><PERSON>(
        onTap: onPressed,
        child: Column(
          children: [
            CustomImage(
              width: MediaQuery.sizeOf(context).width * 0.4,
              height: MediaQuery.sizeOf(context).width * 0.4,
              image: image,
              fromAssets: true,
            ),
            const SizedBox(
              height: 12,
            ),
            Text(
              title,
              style: ThemeManager.medium(
                size: 16,
                color: AppColors.black,
              ),
            ),
            const SizedBox(
              height: 12,
            ),
          ],
        ),
      ),
    );
  }
}
