import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/data_layer/model/announcement_model.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

class AnnouncementDetailsScreen extends StatelessWidget {
  final Announcement announcement;

  const AnnouncementDetailsScreen({Key? key, required this.announcement}) : super(key: key);

  Future<void> _launchMeetingLink() async {
    if (announcement.meetingLink != null) {
      final Uri url = Uri.parse(announcement.meetingLink!);
      if (!await launchUrl(url)) {
        throw Exception('Could not launch $url');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      extendBodyBehindAppBar: announcement.imageUrl != null,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: announcement.imageUrl != null 
            ? Colors.transparent 
            : AppColors.bermuda,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: AppColors.white),
          onPressed: () => Navigator.pop(context),
        ),
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
        ),
        title: Text(
          LocaleKeys.announcement.tr(),
          style: ThemeManager.semiBold(
            size: 20,
            color: AppColors.white,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Hero image with gradient overlay
          if (announcement.imageUrl != null)
            Stack(
              children: [
                SizedBox(
                  height: 240,
                  width: double.infinity,
                  child: Image.network(
                    announcement.imageUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: AppColors.bermuda.withOpacity(0.2),
                        child: const Center(
                          child: Icon(
                            Icons.image_not_supported_outlined,
                            color: AppColors.bermuda,
                            size: 50,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                // Gradient overlay for better text visibility
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          AppColors.black.withOpacity(0.7),
                        ],
                        stops: const [0.6, 1.0],
                      ),
                    ),
                  ),
                ),
                // Title and date on the image
                Positioned(
                  bottom: 20,
                  left: 20,
                  right: 20,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        announcement.title,
                        style: ThemeManager.bold(
                          size: 24,
                          color: AppColors.white,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            color: AppColors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            DateFormat('MMMM d, yyyy').format(announcement.createdAt),
                            style: ThemeManager.medium(
                              size: 14,
                              color: AppColors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          
          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.only(
                top: announcement.imageUrl != null ? 20 : 80,
                left: 20,
                right: 20,
                bottom: 20,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and date (if no image)
                  if (announcement.imageUrl == null) ...[
                    Text(
                      announcement.title,
                      style: ThemeManager.bold(
                        size: 24,
                        color: AppColors.black,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: AppColors.bermuda.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            color: AppColors.bermuda,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            DateFormat('MMMM d, yyyy').format(announcement.createdAt),
                            style: ThemeManager.medium(
                              size: 14,
                              color: AppColors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                  Text(
                    announcement.description,
                    style: ThemeManager.medium(
                      size: 16,
                      color: AppColors.black.withOpacity(0.8),
                    ),
                  ),
                  if (announcement.meetingLink != null) ...[
                    const SizedBox(height: 24),
                    Center(
                      child: ElevatedButton.icon(
                        onPressed: _launchMeetingLink,
                        icon: const Icon(Icons.video_call_rounded),
                        label: Text(LocaleKeys.joinMeeting.tr()),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.bermuda,
                          foregroundColor: AppColors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
