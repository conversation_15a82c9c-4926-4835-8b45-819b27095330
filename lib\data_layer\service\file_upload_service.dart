import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class FileUploadService {
  // Base URL for the API
  final String baseUrl = 'https://aripsalin-api-production.up.railway.app';

  // Upload a file to the server and get the URL
  Future<String?> uploadFile(File file, String fileType) async {
    try {
      // This is a dummy implementation for now
      // In a real implementation, you would upload the file to your storage server

      // Create a multipart request
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/upload/$fileType'),
      );

      // Add the file to the request
      request.files.add(
        await http.MultipartFile.fromPath(
          fileType,
          file.path,
        ),
      );

      // Add user ID to the request (if needed)
      request.fields['userId'] =
          'user_${DateTime.now().millisecondsSinceEpoch}';

      // Send the request
      var response = await request.send();

      // Check if the request was successful
      if (response.statusCode == 200) {
        var responseData = await response.stream.bytesToString();
        var jsonData = jsonDecode(responseData);
        return jsonData['fileUrl'];
      } else {
        debugPrint(
            'Failed to upload file. Status code: ${response.statusCode}');
        return null;
      }
    } catch (error) {
      debugPrint('Error uploading file: $error');
      return null;
    }
  }

  // Dummy implementation for file upload
  // This simulates uploading a file and returning a URL
  Future<String> dummyUploadFile(File file, String fileType) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Return a dummy URL
    return 'https://example.com/uploads/$fileType/${DateTime.now().millisecondsSinceEpoch}.jpg';
  }

  // Upload multiple files and get their URLs
  Future<Map<String, String?>> uploadMultipleFiles({
    required File? profilePicture,
    required File? personalIDFront,
    required File? personalIDBack,
    required File? tazkia,
  }) async {
    Map<String, String?> urls = {};

    // Upload profile image
    if (profilePicture != null) {
      urls['profilePicture'] = await dummyUploadFile(profilePicture, 'profile');
    }

    // Upload personal ID front
    if (personalIDFront != null) {
      urls['personalIDFront'] =
          await dummyUploadFile(personalIDFront, 'id-front');
    }

    // Upload personal ID back
    if (personalIDBack != null) {
      urls['personalIDBack'] = await dummyUploadFile(personalIDBack, 'id-back');
    }

    // Upload tazkia
    if (tazkia != null) {
      urls['tazkia'] = await dummyUploadFile(tazkia, 'tazkia');
    }

    return urls;
  }
}
