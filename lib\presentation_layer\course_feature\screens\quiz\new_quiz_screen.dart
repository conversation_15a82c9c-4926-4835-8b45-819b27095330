import 'dart:convert';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/constants/app_images.dart';
import 'package:arepsalin/data_layer/model/quiz_model.dart';
import 'package:arepsalin/data_layer/provider/new_quiz_provider.dart';
import 'package:arepsalin/data_layer/service/quiz_service.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../widgets/custom_button.dart';
import '../../../widgets/custom_progress_indicator.dart';
import '../../widgets/record_quiz_widget.dart';

class NewQuizScreen extends StatefulWidget {
  const NewQuizScreen({super.key, required this.quiz, required this.quizStatus});

  final Quiz quiz;
  final String quizStatus;
  @override
  State<NewQuizScreen> createState() => _NewQuizScreenState();
}

class _NewQuizScreenState extends State<NewQuizScreen> {
  final TextEditingController _textAnswerController = TextEditingController();
  final QuizService _quizService = QuizService();
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<NewQuizProvider>(context, listen: false).setQuiz(widget.quiz);
    });
  }

  @override
  void dispose() {
    _textAnswerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isRtl = context.locale.languageCode == 'ar';

    return WillPopScope(
      onWillPop: () async {
        return await _showExitWarning(context);
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.close, color: AppColors.primary, size: 28),
            onPressed: () async {
              if (await _showExitWarning(context)) {
                if (!mounted) return;
                Navigator.of(context).pop();
              }
            },
          ),
        ),
        body: SafeArea(
          child: Stack(
            children: [
              SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: Image(
                  image: AssetImage(AppImages.backgroundImage),
                  fit: BoxFit.fill,
                ),
              ),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 15),
                decoration: const BoxDecoration(
                  color: AppColors.transparent,
                ),
                child: Consumer<NewQuizProvider>(
                  builder: (context, quizProvider, child) {
                    if (quizProvider.quiz == null) {
                      return const Center(child: CustomProgressIndicator());
                    }

                    final quiz = quizProvider.quiz!;
                    final currentQuestionIndex =
                        quizProvider.currentQuestionIndex;

                    // Check if quiz has content (questions) or if it's a record quiz
                    if (quiz.content.isEmpty || quiz.isRecord) {
                      // Handle record quizzes
                      if (quiz.isRecord) {
                        return RecordQuizWidget(
                          quiz: quiz,
                          onSubmit: () => _submitQuiz(quizProvider),
                        );
                      } else {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 64,
                                color: AppColors.red,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'لا توجد أسئلة في هذا الاختبار',
                                style: ThemeManager.semiBold(
                                  size: 18,
                                  color: AppColors.black,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 24),
                              CustomButton(
                                title: 'العودة',
                                onPressed: () => Navigator.of(context).pop(),
                                btnColor: AppColors.primary,
                                textStyle: ThemeManager.semiBold(
                                  size: 16,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        );
                      }
                    }

                    final currentQuestion = quiz.content[currentQuestionIndex];
                    final remainingMinutes = quizProvider.remainingMinutes;
                    final remainingSeconds = quizProvider.remainingSeconds;
                    final themeColor =
                        quiz.isFinal ? AppColors.bermuda : AppColors.primary;

                    // Update text controller if needed
                    if (currentQuestion.isText()) {
                      final currentAnswer = quizProvider.getCurrentAnswer();
                      if (currentAnswer != null &&
                          _textAnswerController.text != currentAnswer) {
                        _textAnswerController.text = currentAnswer;
                      }
                    }

                    if (quizProvider.isQuizCompleted) {
                      _submitQuiz(quizProvider);
                      return const Center(child: CustomProgressIndicator());
                    }

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const SizedBox(height: 10),
                        // Progress and Timer
                        Container(
                          margin: const EdgeInsets.symmetric(
                              horizontal: 5, vertical: 10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Question Progress
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.05),
                                      blurRadius: 4,
                                      spreadRadius: 1,
                                    ),
                                  ],
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.question_answer,
                                      color: themeColor,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      '${currentQuestionIndex + 1}/${quiz.content.length}',
                                      style: ThemeManager.semiBold(
                                        size: 16,
                                        color: AppColors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Timer (conditionally shown)
                              if (quizProvider.hasTimer())
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 8),
                                  decoration: BoxDecoration(
                                    color: themeColor,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: themeColor.withOpacity(0.3),
                                        blurRadius: 8,
                                        spreadRadius: 1,
                                      ),
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      Text(
                                        LocaleKeys.timeLeft.tr(),
                                        style: ThemeManager.medium(
                                          size: 14,
                                          color: Colors.white,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        isRtl
                                            ? '$remainingSeconds : $remainingMinutes'
                                            : '$remainingMinutes:${remainingSeconds.toString().padLeft(2, '0')}',
                                        style: ThemeManager.bold(
                                          size: 18,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              else if (quizProvider.isRecordQuiz())
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 8),
                                  decoration: BoxDecoration(
                                    color: themeColor,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: themeColor.withOpacity(0.3),
                                        blurRadius: 8,
                                        spreadRadius: 1,
                                      ),
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.mic,
                                        color: Colors.white,
                                        size: 18,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'تسجيل',
                                        style: ThemeManager.bold(
                                          size: 16,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              else
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 8),
                                  decoration: BoxDecoration(
                                    color: themeColor,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: themeColor.withOpacity(0.3),
                                        blurRadius: 8,
                                        spreadRadius: 1,
                                      ),
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.timer_off,
                                        color: Colors.white,
                                        size: 18,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'بدون وقت',
                                        style: ThemeManager.bold(
                                          size: 16,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                              // Points
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.05),
                                      blurRadius: 4,
                                      spreadRadius: 1,
                                    ),
                                  ],
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.star,
                                      color: themeColor,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      '${currentQuestion.grade}',
                                      style: ThemeManager.semiBold(
                                        size: 16,
                                        color: AppColors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Question Card
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.symmetric(vertical: 15),
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  blurRadius: 10,
                                  spreadRadius: 1,
                                ),
                              ],
                              border: Border.all(
                                color: themeColor.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                // Question Text
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: themeColor.withOpacity(0.05),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: themeColor.withOpacity(0.2),
                                    ),
                                  ),
                                  child: Text(
                                    currentQuestion.question,
                                    style: ThemeManager.semiBold(
                                      size: 18,
                                      color: AppColors.black,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                const SizedBox(height: 20),

                                // Answers
                                Expanded(
                                  child: SingleChildScrollView(
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 5),
                                      child: Column(
                                        children: [
                                          if (currentQuestion.isMCQ())
                                            _buildMCQOptions(currentQuestion,
                                                quizProvider, themeColor)
                                          else if (currentQuestion.isText())
                                            _buildTextInput(
                                                quizProvider, themeColor),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),

                                // Navigation Buttons
                                Padding(
                                  padding: const EdgeInsets.only(top: 15),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      // Previous Button
                                      CustomButton(
                                        borderRadius: 16,
                                        borderColor: currentQuestionIndex > 0
                                            ? themeColor
                                            : AppColors.grey,
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width /
                                                3.3,
                                        textStyle: ThemeManager.semiBold(
                                            size: 16,
                                            color: currentQuestionIndex > 0
                                                ? themeColor
                                                : AppColors.grey
                                                    .withOpacity(0.5)),
                                        btnColor: currentQuestionIndex > 0
                                            ? themeColor.withOpacity(0.1)
                                            : Colors.transparent,
                                        title: LocaleKeys.previous.tr(),
                                        onPressed: () {
                                          if (currentQuestionIndex > 0) {
                                            if (currentQuestion.isText()) {
                                              quizProvider.updateTextAnswer(
                                                  _textAnswerController.text);
                                            }
                                            quizProvider.goToPreviousQuestion();
                                          }
                                        },
                                      ),

                                      // Next/Submit Button
                                      CustomButton(
                                        borderRadius: 16,
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width /
                                                3.3,
                                        textStyle: ThemeManager.semiBold(
                                          size: 16,
                                          color: AppColors.white,
                                        ),
                                        btnColor: quizProvider
                                                .hasAnsweredCurrentQuestion()
                                            ? themeColor
                                            : themeColor.withOpacity(0.5),
                                        title: quizProvider.isQuizFinished()
                                            ? LocaleKeys.submit.tr()
                                            : LocaleKeys.next.tr(),
                                        onPressed: () {
                                          if (quizProvider
                                              .hasAnsweredCurrentQuestion()) {
                                            if (currentQuestion.isText()) {
                                              quizProvider.updateTextAnswer(
                                                  _textAnswerController.text);
                                            }

                                            if (quizProvider.isQuizFinished()) {
                                              _submitQuiz(quizProvider);
                                            } else {
                                              quizProvider.goToNextQuestion();
                                            }
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
              if (_isSubmitting)
                Container(
                  color: Colors.black.withOpacity(0.5),
                  child: const Center(
                    child: CustomProgressIndicator(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool> _showExitWarning(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            LocaleKeys.warningTitle.tr(),
            style: ThemeManager.bold(
              size: 20,
              color: AppColors.primary,
            ),
          ),
          content: Text(
            LocaleKeys.warningMessage.tr(),
            style: ThemeManager.medium(
              size: 16,
              color: AppColors.black,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                LocaleKeys.stayInQuiz.tr(),
                style: ThemeManager.semiBold(
                  size: 16,
                  color: AppColors.primary,
                ),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                LocaleKeys.leaveQuiz.tr(),
                style: ThemeManager.semiBold(
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  Widget _buildMCQOptions(
      QuizQuestion question, NewQuizProvider quizProvider, Color themeColor) {
    final currentAnswer = quizProvider.getCurrentAnswer();

    return Column(
      children: question.answers!.map((answer) {
        final isSelected = currentAnswer == answer.id;

        return InkWell(
          onTap: () {
            quizProvider.updateMCQAnswer(answer.id);
          },
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? themeColor : AppColors.grey,
                width: isSelected ? 2 : 1,
              ),
              color: isSelected ? themeColor.withOpacity(0.1) : Colors.white,
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: themeColor.withOpacity(0.1),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                    ]
                  : null,
            ),
            child: Row(
              children: [
                Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? themeColor : AppColors.grey,
                      width: 2,
                    ),
                    color: isSelected ? themeColor : Colors.white,
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          size: 18,
                          color: Colors.white,
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    answer.text,
                    style: ThemeManager.medium(
                      size: 16,
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTextInput(NewQuizProvider quizProvider, Color themeColor) {
    return TextField(
      controller: _textAnswerController,
      style: ThemeManager.regular(size: 16, color: AppColors.black),
      decoration: InputDecoration(
        hintText: LocaleKeys.enterYourAnswer.tr(),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: themeColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: themeColor, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: themeColor.withOpacity(0.3)),
        ),
        fillColor: themeColor.withOpacity(0.05),
        filled: true,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        hintStyle: ThemeManager.regular(
          size: 16,
          color: AppColors.grey,
        ),
      ),
      maxLines: 6,
      onChanged: (value) {
        quizProvider.updateTextAnswer(value);
      },
    );
  }

  void _showSuccessAndNavigateBack() {
    if (!mounted) return;

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text("${LocaleKeys.submit.tr()} ${LocaleKeys.completed.tr()}"),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 2),
      ),
    );

    // Navigate back to the previous screen
    Navigator.of(context).pop();
    Navigator.of(context).pop();

  }

  Future<void> _submitQuiz(NewQuizProvider quizProvider) async {
    if (_isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Handle record quiz submission
      if (widget.quiz.isRecord && quizProvider.hasAudioFile()) {
        // Upload audio file first
        final audioFile = quizProvider.audioFile!;
        final audioUrl = await _quizService.uploadAudioFile(audioFile);

        if (audioUrl == null) {
          throw Exception('Failed to upload audio file');
        }

        // Submit quiz with audio URL
        final answers = [{
          'type': 'audio',
          'audioUrl': audioUrl,
          'quizId': widget.quiz.id,
          'grade': widget.quiz.grade,
        }];

        final result = await _quizService.submitQuizAttempt(
          widget.quiz.id,
          0, // No time taken for record quizzes
          answers,
          widget.quizStatus == 'redo',
        );

        if (!mounted) return;
        quizProvider.disposeTimer();

        if (result != null) {
          _showSuccessAndNavigateBack();
        } else {
          throw Exception('Failed to submit quiz');
        }
        return;
      }

      // Handle regular quiz submission
      final answers = quizProvider.getAnswersForSubmission();
      final timeTaken = quizProvider.getTimeTaken();
      final isRedo = widget.quizStatus == 'redo'; // Check if quiz is redo type

      // Debug log in development mode
      debugPrint('Submitting quiz with answers: ${jsonEncode({
        'quizId': widget.quiz.id,
        'timeTaken': timeTaken,
        'answers': answers,
        'redo': isRedo,
      })}');

      final result = await _quizService.submitQuizAttempt(
        widget.quiz.id,
        timeTaken,
        answers,
        isRedo, // Pass the redo flag
      );

      if (!mounted) return;

      quizProvider.disposeTimer();

      if (result != null) {
        // Show success message and navigate back
        _showSuccessAndNavigateBack();
      } else {
        setState(() {
          _isSubmitting = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocaleKeys.somethingWentWrong.tr()),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isSubmitting = false;
      });
      debugPrint('Error submitting quiz: $e');

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocaleKeys.somethingWentWrong.tr()),
          backgroundColor: AppColors.red,
        ),
      );
    }
  }

  Widget _buildRecordQuizInterface(Quiz quiz) {
    final themeColor = quiz.isFinal ? AppColors.bermuda : AppColors.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const SizedBox(height: 20),
        // Header
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
            border: Border.all(
              color: themeColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.mic,
                size: 80,
                color: themeColor,
              ),
              const SizedBox(height: 16),
              Text(
                quiz.name,
                style: ThemeManager.bold(
                  size: 24,
                  color: AppColors.black,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'اختبار تسجيل صوتي',
                style: ThemeManager.medium(
                  size: 18,
                  color: themeColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: themeColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'بدون وقت محدد',
                  style: ThemeManager.semiBold(
                    size: 16,
                    color: themeColor,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Instructions
        Expanded(
          child: Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 48,
                    color: themeColor,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'تعليمات الاختبار',
                    style: ThemeManager.bold(
                      size: 20,
                      color: AppColors.black,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'هذا اختبار تسجيل صوتي. يرجى التأكد من وجود ميكروفون يعمل بشكل صحيح قبل البدء.',
                    style: ThemeManager.medium(
                      size: 16,
                      color: AppColors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '• لا يوجد وقت محدد لهذا الاختبار\n• يمكنك التسجيل والتوقف حسب الحاجة\n• تأكد من وضوح الصوت',
                    style: ThemeManager.regular(
                      size: 14,
                      color: AppColors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 30),
                  CustomButton(
                    title: 'بدء التسجيل',
                    onPressed: () {
                      // TODO: Implement audio recording functionality
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('ميزة التسجيل الصوتي قيد التطوير'),
                          backgroundColor: themeColor,
                        ),
                      );
                    },
                    btnColor: themeColor,
                    textStyle: ThemeManager.semiBold(
                      size: 18,
                      color: Colors.white,
                    ),
                    height: 56,
                    borderRadius: 16,
                  ),
                  const SizedBox(height: 16),
                  CustomButton(
                    title: 'العودة',
                    onPressed: () => Navigator.of(context).pop(),
                    btnColor: Colors.transparent,
                    borderColor: themeColor,
                    textStyle: ThemeManager.semiBold(
                      size: 16,
                      color: themeColor,
                    ),
                    height: 48,
                    borderRadius: 16,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
