import 'package:arepsalin/data_layer/model/question_data.dart';

class ExamModel {
  ExamModel(
      {required this.type,
      required this.duration,
      required this.questions,
      required this.maxGrade,
      required this.semester,
      required this.week,
      required this.examID});

  int week;
  int semester;
  String type;
  int duration;
  List<QuestionData> questions;
  int maxGrade;
  String examID;

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'duration': duration,
      'questions': questions.map((question) => question.toJson()).toList(),
      'max-grade': maxGrade,
      'week': week,
      'semester': semester
    };
  }

  factory ExamModel.fromJson(Map<String, dynamic> json,String examID) {
    return ExamModel(
        type: json['type'],
        duration: json['duration'],
        questions: List<QuestionData>.from(json['questions']
            .map((questionJson) => QuestionData.fromJson(questionJson))),
        maxGrade: json['max-grade'],
        week: json['week'],
        semester: json['semester'],
        examID: examID
    );
  }
}
