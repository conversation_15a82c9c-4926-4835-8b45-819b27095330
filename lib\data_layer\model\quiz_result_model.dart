class QuizAnswer {
  final String type;
  final int questionId;
  final int? selectedAnswerId;
  final String? text;

  QuizAnswer({
    required this.type,
    required this.questionId,
    this.selectedAnswerId,
    this.text,
  });

  factory QuizAnswer.fromJson(Map<String, dynamic> json) {
    return QuizAnswer(
      type: json['type'],
      questionId: json['questionId'],
      selectedAnswerId: json['selectedAnswerId'],
      text: json['text'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['type'] = type;
    data['questionId'] = questionId;
    if (selectedAnswerId != null) {
      data['selectedAnswerId'] = selectedAnswerId;
    }
    if (text != null) {
      data['text'] = text;
    }
    return data;
  }
}

class QuizInfo {
  final String name;
  final int grade;

  QuizInfo({
    required this.name,
    required this.grade,
  });

  factory QuizInfo.fromJson(Map<String, dynamic> json) {
    return QuizInfo(
      name: json['name'],
      grade: json['grade'],
    );
  }
}

class StudentInfo {
  final String studentCode;
  final UserInfo user;

  StudentInfo({
    required this.studentCode,
    required this.user,
  });

  factory StudentInfo.fromJson(Map<String, dynamic> json) {
    return StudentInfo(
      studentCode: json['studentCode'],
      user: UserInfo.fromJson(json['user']),
    );
  }
}

class UserInfo {
  final String name;
  final String email;

  UserInfo({
    required this.name,
    required this.email,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      name: json['name'],
      email: json['email'],
    );
  }
}

class AutoGradingDetails {
  final int totalCorrectPoints;
  final int maxPossiblePoints;
  final double percentageCorrect;
  final int finalGrade;

  AutoGradingDetails({
    required this.totalCorrectPoints,
    required this.maxPossiblePoints,
    required this.percentageCorrect,
    required this.finalGrade,
  });

  factory AutoGradingDetails.fromJson(Map<String, dynamic> json) {
    return AutoGradingDetails(
      totalCorrectPoints: json['totalCorrectPoints'],
      maxPossiblePoints: json['maxPossiblePoints'],
      percentageCorrect: json['percentageCorrect'],
      finalGrade: json['finalGrade'],
    );
  }
}

class QuizResult {
  final int id;
  final int studentId;
  final int quizId;
  final List<QuizAnswer> answers;
  final int grade;
  final int timeTaken;
  final bool autoGraded;
  final DateTime createdAt;
  final DateTime updatedAt;
  final QuizInfo quiz;
  final StudentInfo student;
  final AutoGradingDetails autoGradingDetails;

  QuizResult({
    required this.id,
    required this.studentId,
    required this.quizId,
    required this.answers,
    required this.grade,
    required this.timeTaken,
    required this.autoGraded,
    required this.createdAt,
    required this.updatedAt,
    required this.quiz,
    required this.student,
    required this.autoGradingDetails,
  });

  factory QuizResult.fromJson(Map<String, dynamic> json) {
    return QuizResult(
      id: json['id'],
      studentId: json['studentId'],
      quizId: json['quizId'],
      answers: (json['answers'] as List)
          .map((answer) => QuizAnswer.fromJson(answer))
          .toList(),
      grade: json['grade'],
      timeTaken: json['timeTaken'],
      autoGraded: json['autoGraded'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      quiz: QuizInfo.fromJson(json['quiz']),
      student: StudentInfo.fromJson(json['student']),
      autoGradingDetails: AutoGradingDetails.fromJson(json['autoGradingDetails']),
    );
  }
}
