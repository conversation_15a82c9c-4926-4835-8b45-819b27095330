import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../constants/app_images.dart';
import '../../../data_layer/model/enrolled_semester.dart';
import '../../../managers/theme_manager.dart';
import '../widgets/semester_item.dart';

class YearDetailViewScreen extends StatelessWidget {
  const YearDetailViewScreen({
    super.key, 
    required this.year, 
    required this.semesterNo,
    required this.enrolledSemesters,
  });

  final String year;
  final int semesterNo;
  final List<EnrolledSemester> enrolledSemesters;

  @override
  Widget build(BuildContext context) {
    final firstSemesterNo = (semesterNo * 2) - 1;
    final secondSemesterNo = semesterNo * 2;

    // Find the enrolled semester objects
    final firstSemester = enrolledSemesters.firstWhere(
      (sem) => sem.semesterNo == firstSemesterNo,
      orElse: () => EnrolledSemester(id: -1, semesterNo: firstSemesterNo),
    );
    
    final secondSemester = enrolledSemesters.firstWhere(
      (sem) => sem.semesterNo == secondSemesterNo,
      orElse: () => EnrolledSemester(id: -1, semesterNo: secondSemesterNo),
    );

    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        centerTitle: true,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        title: Text(year),
        titleTextStyle:
            ThemeManager.semiBold(size: 20, color: AppColors.warmWhite),
      ),
      body: Stack(
        children: [
          SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: Image(
                image: AssetImage(AppImages.backgroundImage),
                fit: BoxFit.fill,
              )),
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 30.0),
              child: Column(
                children: [
                  SemesterItem(
                    semesterTitle: LocaleKeys.firstSemester.tr(),
                    semesterNo: firstSemesterNo,
                    isUnlocked: firstSemester.id != -1,
                    semesterId: firstSemester.id,
                  ),
                  SemesterItem(
                    semesterTitle: LocaleKeys.secondSemester.tr(),
                    semesterNo: secondSemesterNo,
                    isUnlocked: secondSemester.id != -1,
                    semesterId: secondSemester.id,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
