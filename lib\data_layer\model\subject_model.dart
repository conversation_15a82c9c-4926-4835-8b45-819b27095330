class Subject {
  String id;
  String content;
  String contentType;
  int semester;
  String subjectName;
  int week;
  String lessonName;

  Subject({
    required this.id,
    required this.content,
    required this.contentType,
    required this.semester,
    required this.subjectName,
    required this.week,
    required this.lessonName
  });

  factory Subject.fromJson(String id, Map<String, dynamic> data) {
    return Subject(
        id: id,
        content: data['content'],
        contentType: data['contentType'],
        semester: data['semester'],
        subjectName: data['subjectName'],
        week: data['week'],
        lessonName: data['lessonName']
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'content': content,
      'contentType': contentType,
      'semester': semester,
      'subjectName': subjectName,
      'week': week,
      'lessonName':lessonName
    };
  }
}
