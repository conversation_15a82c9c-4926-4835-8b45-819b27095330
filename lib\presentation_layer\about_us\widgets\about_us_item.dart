import 'package:flutter/material.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';

class AboutUsItem extends StatelessWidget {
  const AboutUsItem(
      {super.key, required this.title, required this.description});

  final String title;
  final String description;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsetsDirectional.symmetric(vertical: 12),
      // decoration: BoxDecoration(
      //   color: AppColors.white,
      //   border: Border.all(color: AppColors.primary),
      //   borderRadius: BorderRadius.circular(5),
      //   // boxShadow: [
      //   //   BoxShadow(
      //   //     color: AppColors.primary.withOpacity(0.2),
      //   //     offset: const Offset(5, 5), // Adjust the offset
      //   //     blurRadius: 10, // Adjust the blur radius
      //   //     spreadRadius: 0.1, // Adjust the spread radius
      //   //   )
      //   // ],
      // ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Icon(
                Icons.ac_unit_outlined,
                color: AppColors.bermuda,
                size: 20,
              ),
              const SizedBox(
                width: 4,
              ),
              Text(
                title,
                style: ThemeManager.bold(
                  color: AppColors.bermuda,
                  size: 22,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                width: 4,
              ),
              const Icon(
                Icons.ac_unit_outlined,
                color: AppColors.bermuda,
                size: 20,
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsetsDirectional.symmetric(
                horizontal: 30, vertical: 5),
            child: Column(
              children: [
                Text(
                  description,
                  style:
                      ThemeManager.semiBold(color: AppColors.black, size: 18),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
