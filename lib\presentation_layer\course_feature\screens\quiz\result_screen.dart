import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../../constants/app_images.dart';
import '../../../widgets/custom_button.dart';

class ResultScreen extends StatelessWidget {
  const ResultScreen(
      {super.key,
      required this.grade,
      required this.maxGrade,
      required this.week,
      required this.semester});

  final int grade;
  final int maxGrade;
  final int week;
  final int semester;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: Image(
                image: AssetImage(AppImages.backgroundImage),
                fit: BoxFit.fill,
              )),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
                  decoration: BoxDecoration(
                      color: AppColors.darkGrey,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: AppColors.primary)),
                  child: Text(
                    '$grade/$maxGrade',
                    textAlign: TextAlign.center,
                    style: ThemeManager.bold(color: AppColors.black, size: 18),
                  ),
                ),
                const SizedBox(
                  height: 32,
                ),
                CustomButton(
                  title: LocaleKeys.ok.tr(),
                  textStyle:
                      ThemeManager.bold(color: AppColors.white, size: 18),
                  onPressed: () {
                    Navigator.pop(context);
                    Navigator.pop(context);
                  },
                  height: 50,
                  borderRadius: 10,
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
