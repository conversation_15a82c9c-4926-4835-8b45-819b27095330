import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:provider/provider.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/data_layer/model/week_quiz_model.dart';
import 'package:arepsalin/data_layer/provider/new_quiz_provider.dart';
import 'package:arepsalin/data_layer/service/quiz_service.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/presentation_layer/course_feature/screens/quiz/new_quiz_screen.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../../constants/app_images.dart';
import '../../../widgets/custom_button.dart';
import '../../../widgets/custom_progress_indicator.dart';

class NewStartQuizScreen extends StatefulWidget {
  const NewStartQuizScreen({super.key, required this.quiz});

  final WeekQuiz quiz;

  @override
  State<NewStartQuizScreen> createState() => _NewStartQuizScreenState();
}

class _NewStartQuizScreenState extends State<NewStartQuizScreen> {
  final QuizService _quizService = QuizService();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    final Color themeColor = widget.quiz.statusColor;

    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: themeColor,
      statusBarIconBrightness: Brightness.dark,
    ));

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        foregroundColor: AppColors.warmWhite,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        title: Text(
          widget.quiz.name,
          style: ThemeManager.medium(color: AppColors.white, size: 22),
        ),
        centerTitle: true,
        backgroundColor: themeColor,
        elevation: 0,
      ),
      body: Stack(
        children: [
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Image(
              image: AssetImage(AppImages.backgroundImage),
              fit: BoxFit.fill,
            ),
          ),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 15,
                        spreadRadius: 2,
                        offset: const Offset(0, 5),
                      ),
                    ],
                    border: Border.all(
                      color: themeColor.withOpacity(0.3),
                      width: 1.5,
                    ),
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: themeColor.withOpacity(0.1),
                          border: Border.all(
                            color: themeColor,
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          widget.quiz.isFinal ? Icons.star : Icons.quiz,
                          color: themeColor,
                          size: 60,
                        ),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        widget.quiz.name,
                        style: ThemeManager.bold(
                          size: 26,
                          color: AppColors.black,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      if (widget.quiz.isFinal) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: themeColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: themeColor,
                            ),
                          ),
                          child: Text(
                            LocaleKeys.final_.tr(),
                            style: ThemeManager.semiBold(
                              size: 14,
                              color: themeColor,
                            ),
                          ),
                        ),
                      ],
                      const SizedBox(height: 24),
                      _buildInfoRow(
                        Icons.subject,
                        widget.quiz.subject?.name ?? "Subject",
                        AppColors.black,
                        themeColor,
                      ),
                      const SizedBox(height: 16),
                      // Conditionally show timer info
                      if (widget.quiz.hasTimer)
                        _buildInfoRow(
                          Icons.timer,
                          '${widget.quiz.timeLimit} ${LocaleKeys.minutes.tr()}',
                          AppColors.black,
                          themeColor,
                        )
                      else if (widget.quiz.isRecordQuiz)
                        _buildInfoRow(
                          Icons.mic,
                          'تسجيل صوتي - بدون وقت محدد',
                          AppColors.black,
                          themeColor,
                        )
                      else
                        _buildInfoRow(
                          Icons.timer_off,
                          'بدون وقت محدد',
                          AppColors.black,
                          themeColor,
                        ),
                      const SizedBox(height: 16),
                      _buildInfoRow(
                        Icons.grade,
                        '${widget.quiz.grade} ${LocaleKeys.points.tr()}',
                        AppColors.black,
                        themeColor,
                      ),
                      const SizedBox(height: 16),
                      _buildInfoRow(
                        Icons.replay,
                        '${widget.quiz.userAttempts}/${widget.quiz.numberOfAttempts} ${LocaleKeys.attempts.tr()}',
                        widget.quiz.isAttemptLimitReached ? AppColors.red : AppColors.black,
                        themeColor,
                      ),
                      if (_errorMessage != null) ...[
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: AppColors.red.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: AppColors.red.withOpacity(0.5),
                            ),
                          ),
                          child: Text(
                            _errorMessage!,
                            style: ThemeManager.medium(
                              size: 14,
                              color: AppColors.red,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                      const SizedBox(height: 30),
                      _isLoading
                          ? const CustomProgressIndicator()
                          : CustomButton(
                              height: 56,
                              width: double.infinity,
                              title: !widget.quiz.isAccessible
                                  ? LocaleKeys.noQuizzesAvailable.tr()
                                  : LocaleKeys.startQuiz.tr(),
                              onPressed: !widget.quiz.isAccessible ? null : _startQuiz,
                              borderRadius: 16,
                              btnColor: !widget.quiz.isAccessible
                                  ? Colors.grey
                                  : themeColor,
                              textStyle: ThemeManager.semiBold(
                                size: 18,
                                color: AppColors.white,
                              ),
                            ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
      IconData icon, String text, Color textColor, Color iconColor) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: iconColor.withOpacity(0.1),
          ),
          child: Icon(
            icon,
            size: 20,
            color: iconColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: ThemeManager.medium(
              size: 16,
              color: textColor,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Future<void> _startQuiz() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Check if quiz is accessible
      if (!widget.quiz.isAccessible) {
        setState(() {
          _isLoading = false;
          _errorMessage = LocaleKeys.noQuizzesAvailable.tr();
        });
        return;
      }

      // Fetch quiz questions
      final quiz = await _quizService.getQuizQuestions(widget.quiz.id);

      if (quiz == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = LocaleKeys.somethingWentWrong.tr();
        });
        return;
      }

      if (!mounted) return;

      // Navigate to quiz screen
      PersistentNavBarNavigator.pushDynamicScreen(
        context,
        screen: MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) => NewQuizProvider(),
            child: NewQuizScreen(quiz: quiz,quizStatus:widget.quiz.status),
          ),
        ),
        withNavBar: false,
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = LocaleKeys.somethingWentWrong.tr();
      });
      debugPrint('Error starting quiz: $e');
    }
  }
}
