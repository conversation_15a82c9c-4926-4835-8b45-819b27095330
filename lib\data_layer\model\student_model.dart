import 'package:arepsalin/data_layer/model/user_model.dart';

class Student {
  final int id;
  final int userId;
  final String studentCode;
  final String city;
  final String church;
  final String abEle3traf;
  final String deaconLevel;
  final String churchService;
  final String qualifications;
  final String personalIDFront;
  final String personalIDBack;
  final bool isVerified;
  final String tazkia;
  final User user;
  final List<dynamic> semester;
  final List<dynamic> completedSemesters;

  Student({
    required this.id,
    required this.userId,
    required this.studentCode,
    required this.city,
    required this.church,
    required this.abEle3traf,
    required this.deaconLevel,
    required this.churchService,
    required this.qualifications,
    required this.personalIDFront,
    required this.personalIDBack,
    required this.isVerified,
    required this.tazkia,
    required this.user,
    required this.semester,
    required this.completedSemesters,
  });

  factory Student.fromJson(Map<String, dynamic> json) {
    return Student(
      id: json['id'],
      userId: json['userId'],
      studentCode: json['studentCode'],
      city: json['city'],
      church: json['church'],
      abEle3traf: json['AbEle3traf'],
      deaconLevel: json['deaconLevel'],
      churchService: json['churchService'],
      qualifications: json['qualifications'],
      personalIDFront: json['personalIDFront'],
      personalIDBack: json['personalIDBack'],
      isVerified: json['isVerified'],
      tazkia: json['Tazkia'],
      user: User.fromJson(json['user']),
      semester: json['semester'],
      completedSemesters: json['CompletedSemesters'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'studentCode': studentCode,
      'city': city,
      'church': church,
      'AbEle3traf': abEle3traf,
      'deaconLevel': deaconLevel,
      'churchService': churchService,
      'qualifications': qualifications,
      'personalIDFront': personalIDFront,
      'personalIDBack': personalIDBack,
      'isVerified': isVerified,
      'Tazkia': tazkia,
      'user': user.toJson(),
      'semester': semester,
      'CompletedSemesters': completedSemesters,
    };
  }
}