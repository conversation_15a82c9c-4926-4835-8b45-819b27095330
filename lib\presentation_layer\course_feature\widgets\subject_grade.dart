import 'package:flutter/material.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';

class SubjectGrade extends StatelessWidget {
  const SubjectGrade(
      {super.key, required this.grade, required this.subjectName});

  final String subjectName;

  final String grade;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 2),
      child: Row(
        children: [
          Text(
            subjectName,
            style: ThemeManager.semiBold(size: 14, color: AppColors.black),
          ),
          const Spacer(),
          Text(
            grade,
            style: ThemeManager.semiBold(size: 14, color: AppColors.black),
          ),
        ],
      ),
    );
  }
}
