abstract class LocaleKeys {
  static const home = 'home';
  static const email = 'email';
  static const pass = 'pass';
  static const name = 'name';
  static const level = 'level';
  static const enterValidEmail = 'enter_validEmail';
  static const enterValidName = 'enter_validName';
  static const passValidation = 'pass_validation';
  static const register = 'register';
  static const createNewAccount = 'create_new_account';
  static const login = 'login';
  static const passIsForgotten = 'passIs_forgotten';
  static const wrongPass = 'wrong_pass';
  static const notRegistered = 'not_registered';
  static const birthBate = "birth_date";
  static const phoneNumber = "phone_number";
  static const confessionFather = "confession_father";
  static const churchService = "church_service";
  static const qualification = "qualification";
  static const deaconessRank = "deaconess_rank";
  static const job = "job";
  static const enterPhoneNumber = "enter_phone_number";
  static const enterConfessionFather = "enter_confession_father";
  static const enterChurchService = "enter_church_service";
  static const enterQualification = "enter_qualification";
  static const enterDeaconessRank = "enter_deaconess_rank";
  static const enterJob = "enter_job";
  static const enterEmail = "enter_email";
  static const enterPass = "enter_pass";
  static const enterFullName = "enter_full_name";
  static const enterAddress = "enter_address";
  static const address = "address";
  static const save = "save";
  static const cancel = "cancel";
  static const userExists = "user_exists";
  static const weakPass = "weak_pass";
  static const plzWait = "plz_wait";
  static const userNotExist = "user_not_exist";
  static const somethingWentWrong = "something_went_wrong";
  static const checkEmail = "check_email";
  static const enterBirthdate = "enter_birthdate";
  static const takePic = 'take_picture';
  static const conditions = 'conditions';
  static const military = 'military';
  static const fatherAccept = 'father_accept';
  static const onlineTest = 'online_test';
  static const gizaUsage = 'giza_usage';
  static const nahdaUsage = 'nahda_usage';
  static const formFill = 'form_fill';
  static const minAge = 'min_age';
  static const continueButton = 'continue';
  static const readAboutUs = 'read_about_us';
  static const choseImage = 'chose_image';
  static const firstYear = "first_year";
  static const secondYear = "second_year";
  static const thirdYear = "third_year";
  static const fourthYear = "fourth_year";
  static const fifthYear = "fifth_year";
  static const week = "week";
  static const firstSemester = "first_semester";
  static const secondSemester = "second_semester";
  static const welcome = 'welcome';
  static const videos = "videos";

  static const audios = "audios";
  static const content = "content";

  static const tests = "tests";

  static const next = "next";
  static const previous = "previous";
  static const submit = "submit";
  static const startQuiz = "start_quiz";

  static const points = "points";

  static const userScreen = 'user_screen';

  static const lang = 'lang';
  static const grades = 'grades';
  static const logOut = 'log_out';
  static const ok = 'ok';
  static const euchologion = "euchologion";
  static const psalmody = "psalmody";
  static const courses = "courses";
  static const aboutUs = 'about_us';
  static const weekNumber = "week_number";
  static const congratulationsYouPassedInThisExam =
      "congratulations_You_passed_in_this_exam";
  static const sorryYouHaveReachedMaxLimitOfTrial =
      "sorry_you_have_reached_max_limit_of_trial";
  static const vision = "vision";
  static const visionValue = "visionValue";
  static const message = "message";
  static const messageValue = "messageValue";
  static const values = "values";
  static const valuesValue = "valuesValue";
  static const loveFirstly = "loveFirstly";
  static const workTogether = "workTogether";
  static const churchAffiliation = "churchAffiliation";
  static const accountability = "accountability";
  static const transparency = "transparency";
  static const withPrays = "";

  static const availableSubjects = 'available_subjects';
  static const noSubjectsAvailable = 'no_subjects_available';
  static const lessonsCount = 'lessons_count';
  static const subjectCode = 'subject_code';
  static const subjectName = 'subject_name';
  static const subjectDescription = 'subject_description';
  static const startLesson = 'start_lesson';
  static const continueLesson = 'continue_lesson';
  static const completed = 'completed';
  static const inProgress = 'in_progress';
  static const notStarted = 'not_started';
  static const noContentAvailable = 'no_content_available';
  static const noAudioAvailable = 'no_audio_available';
  static const noVideoAvailable = 'noVideoAvailable';
  static const noQuizzesAvailable = 'no_quizzes_available';
  static const minutes = 'minutes';
  static const attempts = 'attempts';
  static const final_ = 'final';
  static const enterYourAnswer = 'enter_your_answer';
  static const sorryYouFailed = 'sorry_you_failed';
  static const timeLeft = 'time_left';
  static const warningTitle = 'warning_title';
  static const warningMessage = 'warning_message';
  static const stayInQuiz = 'stay_in_quiz';
  static const leaveQuiz = 'leave_quiz';
  static const correct = 'correct';

  static const String announcement = 'announcement';
  static const String joinMeeting = 'joinMeeting';
  static const String failedToLoadAnnouncements = 'failedToLoadAnnouncements';
  static const String noAnnouncementsAvailable = 'noAnnouncementsAvailable';
}
