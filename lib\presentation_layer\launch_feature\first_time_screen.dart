import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/constants/app_images.dart';
import 'package:arepsalin/managers/shared_pref_manager.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/presentation_layer/auth_feature/screens/login_screen.dart';
import 'package:arepsalin/presentation_layer/widgets/custom_button.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../about_us/screens/about_us_screen.dart';
import '../widgets/condition_item.dart';

class FirstTimeScreen extends StatelessWidget {
  const FirstTimeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: AppColors.transparent,
      statusBarIconBrightness: Brightness.light,
    ));
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Image(
            image: AssetImage(AppImages.logo),
            height: MediaQuery.sizeOf(context).height / 3.3,
          ),
          Text(
            LocaleKeys.conditions.tr(),
            textAlign: TextAlign.center,
            style: ThemeManager.bold(size: 20, color: AppColors.black),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              children: [
                ConditionItem(condition: LocaleKeys.minAge.tr()),
                ConditionItem(condition: LocaleKeys.military.tr()),
                ConditionItem(condition: LocaleKeys.fatherAccept.tr()),
                ConditionItem(condition: LocaleKeys.onlineTest.tr()),
                ConditionItem(condition: LocaleKeys.gizaUsage.tr()),
                ConditionItem(condition: LocaleKeys.nahdaUsage.tr()),
                ConditionItem(condition: LocaleKeys.formFill.tr()),
              ],
            ),
          ),
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              decoration: BoxDecoration(boxShadow: [
                BoxShadow(
                    color: AppColors.black.withOpacity(0.1),
                    spreadRadius: 1,
                    offset: const Offset(0, 5),
                    blurRadius: 4)
              ]),
              child: CustomButton(
                  title: LocaleKeys.readAboutUs.tr(),
                  textStyle: ThemeManager.medium(size: 22),
                  btnColor: AppColors.white,
                  borderRadius: 10,
                  height: 50,
                  onPressed: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AboutUsScreen(),
                        ));
                  })),
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              decoration: BoxDecoration(boxShadow: [
                BoxShadow(
                    color: AppColors.black.withOpacity(0.1),
                    spreadRadius: 1,
                    offset: const Offset(0, 5),
                    blurRadius: 4)
              ]),
              child: CustomButton(
                  title: LocaleKeys.continueButton.tr(),
                  textStyle:
                      ThemeManager.medium(size: 22, color: AppColors.white),
                  borderRadius: 10,
                  height: 50,
                  onPressed: () {
                    Navigator.of(context).pushReplacement(MaterialPageRoute(
                        builder: (context) => const LoginScreen()));
                    SharedPrefManager s = SharedPrefManager();
                    s.write('isFirstTime', 'no');
                  }))
        ],
      ),
    );
  }
}
