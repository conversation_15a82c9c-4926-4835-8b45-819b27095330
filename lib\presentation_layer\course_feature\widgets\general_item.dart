import 'package:flutter/material.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/managers/theme_manager.dart';

class GeneralItem extends StatelessWidget {
  const GeneralItem(
      {Key? key,
      required this.title,
      required this.icon,
      required this.onTabBackFunction})
      : super(key: key);

  final String title;

  final IconData icon;

  final Function() onTabBackFunction;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: InkWell(
        onTap: onTabBackFunction,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    title,
                    overflow: TextOverflow.visible,
                    style:
                        ThemeManager.semiBold(size: 18, color: AppColors.black),
                  ),
                ],
              ),
            ),
            const SizedBox(
              width: 5,
            ),
            Icon(
              icon,
              color: AppColors.black,
            ),
          ],
        ),
      ),
    );
  }
}
