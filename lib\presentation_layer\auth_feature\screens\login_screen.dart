import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:arepsalin/constants/app_images.dart';
import 'package:arepsalin/data_layer/provider/authentication_provider.dart';
import 'package:arepsalin/data_layer/service/authentication_service.dart';
import 'package:arepsalin/managers/shared_pref_manager.dart';
import 'package:arepsalin/presentation_layer/auth_feature/screens/signup_screen.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_form_field.dart';
import '../../widgets/snackbar_widget.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final AuthenticationService _authenticationService = AuthenticationService();

  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return LocaleKeys.enterValidEmail.tr();
    }
    final emailRegex = RegExp(r'^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$');

    if (!emailRegex.hasMatch(value)) {
      return LocaleKeys.enterValidEmail.tr();
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: AppColors.transparent,
      statusBarIconBrightness: Brightness.dark,
    ));
    Size size = MediaQuery.sizeOf(context);
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: SingleChildScrollView(
          child: Container(
            decoration: const BoxDecoration(color: AppColors.white),
            height: size.height,
            width: double.infinity,
            padding: const EdgeInsets.all(12.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Center(
                    child: SizedBox(
                        width: size.width - 100,
                        child: Image(
                            image: AssetImage(AppImages.logo),
                            fit: BoxFit.contain)),
                  ),
                  CustomTextFormField(
                    label: LocaleKeys.email.tr(),
                    hint: LocaleKeys.enterEmail.tr(),
                    keyboardType: TextInputType.emailAddress,
                    controller: _emailController,
                    validator: validateEmail,
                    withBorder: true,
                  ),
                  const SizedBox(height: 15),
                  Consumer<AuthenticationProvider>(
                      builder: (context, provider, child) {
                    return CustomTextFormField(
                      suffix: Padding(
                        padding: const EdgeInsetsDirectional.only(
                            start: 15, end: 15),
                        child: InkWell(
                          child: provider.showPassword
                              ? const Icon(
                                  Icons.visibility_off_outlined,
                                  color: AppColors.borderGrey,
                                  size: 22,
                                )
                              : const Icon(
                                  Icons.visibility_outlined,
                                  color: AppColors.borderGrey,
                                  size: 22,
                                ),
                          onTap: () {
                            provider.togglePasswordVisibility();
                          },
                        ),
                      ),
                      label: LocaleKeys.pass.tr(),
                      hint: LocaleKeys.enterPass.tr(),
                      obscureText: provider.showPassword,
                      controller: _passwordController,
                      validator: (value) {
                        if (value == null || value.length < 8) {
                          return LocaleKeys.passValidation.tr();
                        }
                        return null;
                      },
                      withBorder: true,
                    );
                  }),
                  const SizedBox(height: 25),
                  CustomButton(
                    height: 50,
                    borderRadius: 10,
                    onPressed: () async {
                      _authenticationService.login(_formKey, context,
                          _emailController.text, _passwordController.text);
                      SharedPrefManager s = SharedPrefManager();
                      s.write('userAuthorized', 'Authorized');
                    },
                    title: LocaleKeys.login.tr(),
                    textStyle:
                        ThemeManager.semiBold(color: AppColors.white, size: 16),
                  ),
                  const SizedBox(height: 10),
                  CustomButton(
                      title: LocaleKeys.createNewAccount.tr(),
                      borderColor: AppColors.transparent,
                      btnColor: AppColors.transparent,
                      textStyle:
                          ThemeManager.medium(size: 14, color: AppColors.black),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const SignUpScreen()),
                        );
                      }),
                  CustomButton(
                    title: LocaleKeys.passIsForgotten.tr(),
                    borderColor: AppColors.transparent,
                    btnColor: AppColors.transparent,
                    textStyle:
                        ThemeManager.medium(size: 14, color: AppColors.black),
                    onPressed: () {
                      final emailValidationResult =
                          validateEmail(_emailController.text);

                      if (emailValidationResult == null) {
                        _authenticationService.resetPassword(
                            _emailController.text, context);
                      } else {
                        SnackBarWidget(
                          message: LocaleKeys.enterValidEmail.tr(),
                          context: context,
                          backgroundColor: AppColors.black,
                          messageColor: AppColors.white,
                          messageSize: 16,
                        ).showCustomSnackBar();
                      }
                    },
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
