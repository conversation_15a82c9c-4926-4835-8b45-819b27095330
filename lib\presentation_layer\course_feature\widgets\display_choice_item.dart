import 'package:flutter/material.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';

class DisplayChoiceItem extends StatelessWidget {
  final String word;
  final bool isSelected;
  final Function onTap;
  final bool isLast;

  const DisplayChoiceItem(
      {super.key,
      required this.word,
      required this.isSelected,
      required this.onTap,
      this.isLast = false});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: () => onTap(),
          child: Container(
            width: 109,
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
                color: isSelected ? AppColors.primary : AppColors.grey,
                borderRadius: BorderRadius.circular(10)),
            padding: const EdgeInsetsDirectional.symmetric(
              vertical: 7,
            ),
            child: Text(
              textAlign: TextAlign.center,
              word,
              style: ThemeManager.medium(
                size: 13,
                color: isSelected ? AppColors.white : AppColors.black,
              ),
            ),
          ),
        ),
        !isSelected && !isLast
            ? Container(
                width: 1.0,
                height: 16,
                color: AppColors.black,
              )
            : Container(
                width: 1.0,
                color: Colors.transparent,
              )
      ],
    );
  }
}
