import 'package:flutter_test/flutter_test.dart';
import 'package:arepsalin/data_layer/provider/new_quiz_provider.dart';
import 'package:arepsalin/data_layer/model/quiz_model.dart';

void main() {
  group('NewQuizProvider Tests', () {
    late NewQuizProvider provider;

    setUp(() {
      provider = NewQuizProvider();
    });

    test('should handle quiz with timeLimit (regular quiz)', () {
      // Arrange
      final quiz = Quiz(
        id: 1,
        name: 'Timed Quiz',
        grade: 100,
        type: 'regular',
        numberOfAttempts: 2,
        timeLimit: 30, // Has timeLimit
        subject: QuizSubject(id: 1, name: 'Math', code: 'MATH101'),
        lesson: QuizLesson(id: 1, name: 'Algebra'),
        content: [
          QuizQuestion(
            questionId: 1,
            question: 'What is 2+2?',
            type: 'mcq',
            grade: 10,
            answers: [
              QuizAnswer(id: 1, text: '3'),
              QuizAnswer(id: 2, text: '4'),
            ],
            correctAnswerId: 2,
          ),
        ],
        isRecord: false,
      );

      // Act
      provider.setQuiz(quiz);

      // Assert
      expect(provider.hasTimer(), isTrue);
      expect(provider.isRecordQuiz(), isFalse);
      expect(provider.remainingMinutes, equals(30));
    });

    test('should handle quiz without timeLimit', () {
      // Arrange
      final quiz = Quiz(
        id: 1,
        name: 'No Timer Quiz',
        grade: 100,
        type: 'regular',
        numberOfAttempts: 2,
        timeLimit: null, // No timeLimit
        subject: QuizSubject(id: 1, name: 'Math', code: 'MATH101'),
        lesson: QuizLesson(id: 1, name: 'Algebra'),
        content: [
          QuizQuestion(
            questionId: 1,
            question: 'What is 2+2?',
            type: 'mcq',
            grade: 10,
            answers: [
              QuizAnswer(id: 1, text: '3'),
              QuizAnswer(id: 2, text: '4'),
            ],
            correctAnswerId: 2,
          ),
        ],
        isRecord: false,
      );

      // Act
      provider.setQuiz(quiz);

      // Assert
      expect(provider.hasTimer(), isFalse);
      expect(provider.isRecordQuiz(), isFalse);
      expect(provider.remainingMinutes, equals(0));
    });

    test('should handle record quiz', () {
      // Arrange
      final quiz = Quiz(
        id: 1,
        name: 'Record Quiz',
        grade: 50,
        type: 'record',
        numberOfAttempts: 1,
        timeLimit: null,
        subject: QuizSubject(id: 1, name: 'Arabic', code: 'AR101'),
        lesson: QuizLesson(id: 1, name: 'Recitation'),
        content: [
          QuizQuestion(
            questionId: 1,
            question: 'Record your recitation',
            type: 'text',
            grade: 50,
          ),
        ],
        isRecord: true,
      );

      // Act
      provider.setQuiz(quiz);

      // Assert
      expect(provider.hasTimer(), isFalse);
      expect(provider.isRecordQuiz(), isTrue);
      expect(provider.remainingMinutes, equals(0));
    });

    test('should handle record quiz even with timeLimit (timer should be disabled)', () {
      // Arrange
      final quiz = Quiz(
        id: 1,
        name: 'Record Quiz with TimeLimit',
        grade: 50,
        type: 'record',
        numberOfAttempts: 1,
        timeLimit: 30, // Has timeLimit but is record quiz
        subject: QuizSubject(id: 1, name: 'Arabic', code: 'AR101'),
        lesson: QuizLesson(id: 1, name: 'Recitation'),
        content: [
          QuizQuestion(
            questionId: 1,
            question: 'Record your recitation',
            type: 'text',
            grade: 50,
          ),
        ],
        isRecord: true,
      );

      // Act
      provider.setQuiz(quiz);

      // Assert
      expect(provider.hasTimer(), isFalse); // Timer should be disabled for record quizzes
      expect(provider.isRecordQuiz(), isTrue);
      expect(provider.remainingMinutes, equals(0));
    });

    test('should return 0 for getTimeTaken when no timeLimit', () {
      // Arrange
      final quiz = Quiz(
        id: 1,
        name: 'No Timer Quiz',
        grade: 100,
        type: 'regular',
        numberOfAttempts: 2,
        timeLimit: null,
        subject: QuizSubject(id: 1, name: 'Math', code: 'MATH101'),
        lesson: QuizLesson(id: 1, name: 'Algebra'),
        content: [],
        isRecord: false,
      );

      // Act
      provider.setQuiz(quiz);
      final timeTaken = provider.getTimeTaken();

      // Assert
      expect(timeTaken, equals(0));
    });

    test('should handle MCQ answers correctly', () {
      // Arrange
      final quiz = Quiz(
        id: 1,
        name: 'MCQ Quiz',
        grade: 100,
        type: 'regular',
        numberOfAttempts: 2,
        timeLimit: null,
        subject: QuizSubject(id: 1, name: 'Math', code: 'MATH101'),
        lesson: QuizLesson(id: 1, name: 'Algebra'),
        content: [
          QuizQuestion(
            questionId: 1,
            question: 'What is 2+2?',
            type: 'mcq',
            grade: 10,
            answers: [
              QuizAnswer(id: 1, text: '3'),
              QuizAnswer(id: 2, text: '4'),
            ],
            correctAnswerId: 2,
          ),
        ],
        isRecord: false,
      );

      // Act
      provider.setQuiz(quiz);
      provider.updateMCQAnswer(2);

      // Assert
      expect(provider.hasAnsweredCurrentQuestion(), isTrue);
      expect(provider.getCurrentAnswer(), equals(2));
    });

    test('should handle text answers correctly', () {
      // Arrange
      final quiz = Quiz(
        id: 1,
        name: 'Text Quiz',
        grade: 100,
        type: 'regular',
        numberOfAttempts: 2,
        timeLimit: null,
        subject: QuizSubject(id: 1, name: 'English', code: 'EN101'),
        lesson: QuizLesson(id: 1, name: 'Writing'),
        content: [
          QuizQuestion(
            questionId: 1,
            question: 'Describe your day',
            type: 'text',
            grade: 20,
          ),
        ],
        isRecord: false,
      );

      // Act
      provider.setQuiz(quiz);
      provider.updateTextAnswer('It was a great day!');

      // Assert
      expect(provider.hasAnsweredCurrentQuestion(), isTrue);
      expect(provider.getCurrentAnswer(), equals('It was a great day!'));
    });
  });
}
