import 'dart:convert';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:arepsalin/presentation_layer/auth_feature/screens/login_screen.dart';
import 'package:arepsalin/presentation_layer/home_feature/screens/initial_screen.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../constants/app_colors.dart';
import '../../managers/secure_storage.dart';
import '../../presentation_layer/widgets/snackbar_widget.dart';

class AuthenticationService {
  Future<void> login(GlobalKey<FormState> formKey, BuildContext context,
      String email, String pass) async {
    if (formKey.currentState!.validate()) {
      try {
        final response = await http.post(
          Uri.parse(
              'https://aripsalin-api-production.up.railway.app/auth/login'),
          headers: <String, String>{
            'Content-Type': 'application/json',
          },
          body: jsonEncode(<String, String>{
            'email': email,
            'password': pass,
          }),
        );
        debugPrint('Login response: ${response.body}');

        if (response.statusCode == 200) {
          final jsonResponse = jsonDecode(response.body);
          final accessToken = jsonResponse['access_token'];
          final refreshToken = jsonResponse['refresh_token'];
          debugPrint('Login successful, token received');

          SecureStorage storage = SecureStorage();
          await storage.saveToken('access_token', accessToken);
          await storage.saveToken('refresh_token', refreshToken);

          if (context.mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const InitialScreen()),
            );
          }
        } else if (response.statusCode == 403) {
          if (context.mounted) {
            SnackBarWidget(
              message: LocaleKeys.userNotExist.tr(),
              context: context,
              backgroundColor: AppColors.black,
              messageColor: AppColors.white,
              messageSize: 16,
            ).showCustomSnackBar();
          }
        } else {
          if (context.mounted) {
            SnackBarWidget(
              message: LocaleKeys.somethingWentWrong.tr(),
              context: context,
              backgroundColor: AppColors.red,
              messageColor: AppColors.white,
              messageSize: 16,
            ).showCustomSnackBar();
          }
        }
      } catch (e) {
        debugPrint('Error during login: $e');
        if (context.mounted) {
          SnackBarWidget(
            message: LocaleKeys.somethingWentWrong.tr(),
            context: context,
            backgroundColor: AppColors.red,
            messageColor: AppColors.white,
            messageSize: 16,
          ).showCustomSnackBar();
        }
      }
    }
  }

  Future<String?> uploadImage(String userId, File imageFile) async {
    try {
      // Create a multipart request
      var request = http.MultipartRequest(
        'POST',
        Uri.parse(
            'https://aripsalin-api-production.up.railway.app/upload/image'),
      );

      // Add the file to the request
      request.files.add(
        await http.MultipartFile.fromPath(
          'image',
          imageFile.path,
        ),
      );

      // Add user ID to the request
      request.fields['userId'] = userId;

      // Send the request
      var response = await request.send();

      // Check if the request was successful
      if (response.statusCode == 200) {
        var responseData = await response.stream.bytesToString();
        var jsonData = jsonDecode(responseData);
        return jsonData['imageUrl'];
      } else {
        debugPrint(
            'Failed to upload image. Status code: ${response.statusCode}');
        return null;
      }
    } catch (error) {
      debugPrint('Error uploading image: $error');
      return null;
    }
  }

  Future<void> signUp({
    required BuildContext context,
    required GlobalKey<FormState> formKey,
    required String email,
    required String name,
    required String pass,
    required String address,
    required String job,
    required String phone,
    required String father,
    required String rank,
    required String qualification,
    required String service,
    required String birthDate,
    required File imageFile,
  }) async {
    if (formKey.currentState!.validate()) {
      try {
        SnackBarWidget(
          message: LocaleKeys.plzWait.tr(),
          context: context,
          backgroundColor: AppColors.black,
          messageColor: AppColors.white,
          messageSize: 16,
        ).showCustomSnackBar();

        // Upload image first
        String? imageUrl = await uploadImage(
            'user_${DateTime.now().millisecondsSinceEpoch}', imageFile);

        // Register user
        final response = await http.post(
          Uri.parse(
              'https://aripsalin-api-production.up.railway.app/auth/register'),
          headers: <String, String>{
            'Content-Type': 'application/json',
          },
          body: jsonEncode(<String, dynamic>{
            'email': email,
            'password': pass,
            'name': name,
            'address': address,
            'job': job,
            'phone': phone,
            'father': father,
            'rank': rank,
            'qualification': qualification,
            'service': service,
            'birthDate': birthDate,
            'imageUrl': imageUrl,
          }),
        );

        if (response.statusCode == 201) {
          // Registration successful
          if (context.mounted) {
            PersistentNavBarNavigator.pushNewScreen(
              context,
              screen: const LoginScreen(),
              withNavBar: false,
              pageTransitionAnimation: PageTransitionAnimation.cupertino,
            );
          }
        } else if (response.statusCode == 400) {
          // Bad request - likely email already exists
          if (context.mounted) {
            SnackBarWidget(
              message: LocaleKeys.userExists.tr(),
              context: context,
              backgroundColor: AppColors.red,
              messageColor: AppColors.white,
              messageSize: 20,
            ).showCustomSnackBar();
          }
        } else {
          // Other error
          if (context.mounted) {
            SnackBarWidget(
              message: LocaleKeys.somethingWentWrong.tr(),
              context: context,
              backgroundColor: AppColors.red,
              messageColor: AppColors.white,
              messageSize: 16,
            ).showCustomSnackBar();
          }
        }
      } catch (e) {
        debugPrint('Error during signup: $e');
        if (context.mounted) {
          SnackBarWidget(
            message: LocaleKeys.somethingWentWrong.tr(),
            context: context,
            backgroundColor: AppColors.red,
            messageColor: AppColors.white,
            messageSize: 16,
          ).showCustomSnackBar();
        }
      }
    }
  }

  Future<void> resetPassword(String email, BuildContext context) async {
    try {
      final response = await http.post(
        Uri.parse(
            'https://aripsalin-api-production.up.railway.app/auth/reset-password'),
        headers: <String, String>{
          'Content-Type': 'application/json',
        },
        body: jsonEncode(<String, String>{
          'email': email,
        }),
      );

      if (response.statusCode == 200) {
        if (context.mounted) {
          SnackBarWidget(
            message: LocaleKeys.checkEmail.tr(),
            context: context,
            backgroundColor: AppColors.black,
            messageColor: AppColors.white,
            messageSize: 16,
          ).showCustomSnackBar();
        }
      } else {
        if (context.mounted) {
          SnackBarWidget(
            message: LocaleKeys.somethingWentWrong.tr(),
            context: context,
            backgroundColor: AppColors.black,
            messageColor: AppColors.white,
            messageSize: 16,
          ).showCustomSnackBar();
        }
      }
    } catch (e) {
      debugPrint('Error resetting password: $e');
      if (context.mounted) {
        SnackBarWidget(
          message: LocaleKeys.somethingWentWrong.tr(),
          context: context,
          backgroundColor: AppColors.black,
          messageColor: AppColors.white,
          messageSize: 16,
        ).showCustomSnackBar();
      }
    }
  }
}
