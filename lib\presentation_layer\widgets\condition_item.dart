import 'package:flutter/material.dart';

import '../../constants/app_colors.dart';
import '../../managers/theme_manager.dart';

class ConditionItem extends StatelessWidget {
  const ConditionItem({Key? key, required this.condition}) : super(key: key);
  final String condition;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3.5),
      child: Row(
        children: [
          const Icon(
            Icons.fiber_manual_record_sharp,
            size: 20,
            color: AppColors.primary,
          ),
          const SizedBox(
            width: 13,
          ),
          Expanded(
            child: Text(
              condition,
              overflow: TextOverflow.visible,
              style: ThemeManager.regular(size: 18, color: AppColors.black),
            ),
          )
        ],
      ),
    );
  }
}
