import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:arepsalin/constants/app_images.dart';
import 'package:arepsalin/presentation_layer/about_us/widgets/about_us_item.dart';
import 'package:arepsalin/presentation_layer/about_us/widgets/about_us_values.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';

class AboutUsScreen extends StatefulWidget {
  const AboutUsScreen({super.key});

  @override
  AboutUsScreenState createState() => AboutUsScreenState();
}

class AboutUsScreenState extends State<AboutUsScreen> {
  late YoutubePlayerController _controller;
  late String _videoId;

  @override
  void initState() {
    super.initState();
    // Extract video ID from the YouTube URL
    _videoId = YoutubePlayer.convertUrlToId(
            'https://www.youtube.com/watch?v=-FIO80TxYE0') ??
        '';

    _controller = YoutubePlayerController(
      initialVideoId: _videoId,
      flags: const YoutubePlayerFlags(
        autoPlay: false,
        mute: false,
        enableCaption: true,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        centerTitle: true,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        title: Text(LocaleKeys.aboutUs.tr()),
        titleTextStyle:
            ThemeManager.semiBold(size: 20, color: AppColors.warmWhite),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Column(
          children: [
            const SizedBox(height: 10),
            Image.asset(AppImages.aripsalinTopImage,
                width: double.infinity, fit: BoxFit.fitWidth),
            const SizedBox(height: 10),
            YoutubePlayer(
              controller: _controller,
              showVideoProgressIndicator: true,
              progressIndicatorColor: AppColors.primary,
              progressColors: const ProgressBarColors(
                playedColor: AppColors.primary,
                handleColor: AppColors.primary,
              ),
              onReady: () {
                // Player is ready
              },
            ),
            const SizedBox(height: 10),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    AboutUsItem(
                        title: LocaleKeys.vision.tr(),
                        description: LocaleKeys.visionValue.tr()),
                    AboutUsItem(
                        title: LocaleKeys.message.tr(),
                        description: LocaleKeys.messageValue.tr()),
                    const AboutUsValues(),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
