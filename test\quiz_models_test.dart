import 'package:flutter_test/flutter_test.dart';
import 'package:arepsalin/data_layer/model/quiz_model.dart';
import 'package:arepsalin/data_layer/model/week_quiz_model.dart';

void main() {
  group('Quiz Model Tests', () {
    test('should create Quiz with optional timeLimit', () {
      // Arrange & Act
      final quiz = Quiz(
        id: 1,
        name: 'Test Quiz',
        grade: 100,
        type: 'regular',
        numberOfAttempts: 3,
        timeLimit: null, // Optional timeLimit
        subject: QuizSubject(id: 1, name: 'Math', code: 'MATH101'),
        lesson: QuizLesson(id: 1, name: 'Algebra'),
        content: [],
        isRecord: false,
      );

      // Assert
      expect(quiz.timeLimit, isNull);
      expect(quiz.isRecord, isFalse);
      expect(quiz.hasTimer, isFalse);
      expect(quiz.isRecordQuiz, isFalse);
    });

    test('should create record Quiz', () {
      // Arrange & Act
      final quiz = Quiz(
        id: 1,
        name: 'Record Quiz',
        grade: 50,
        type: 'record',
        numberOfAttempts: 1,
        timeLimit: null,
        subject: QuizSubject(id: 1, name: 'Arabic', code: 'AR101'),
        lesson: QuizLesson(id: 1, name: 'Recitation'),
        content: [],
        isRecord: true,
      );

      // Assert
      expect(quiz.isRecord, isTrue);
      expect(quiz.hasTimer, isFalse);
      expect(quiz.isRecordQuiz, isTrue);
    });

    test('should create Quiz with timer', () {
      // Arrange & Act
      final quiz = Quiz(
        id: 1,
        name: 'Timed Quiz',
        grade: 100,
        type: 'regular',
        numberOfAttempts: 2,
        timeLimit: 30,
        subject: QuizSubject(id: 1, name: 'Science', code: 'SCI101'),
        lesson: QuizLesson(id: 1, name: 'Physics'),
        content: [],
        isRecord: false,
      );

      // Assert
      expect(quiz.timeLimit, equals(30));
      expect(quiz.hasTimer, isTrue);
      expect(quiz.isRecordQuiz, isFalse);
    });

    test('should serialize and deserialize Quiz correctly', () {
      // Arrange
      final originalQuiz = Quiz(
        id: 1,
        name: 'Test Quiz',
        grade: 100,
        type: 'regular',
        numberOfAttempts: 3,
        timeLimit: 45,
        subject: QuizSubject(id: 1, name: 'Math', code: 'MATH101'),
        lesson: QuizLesson(id: 1, name: 'Algebra'),
        content: [],
        isRecord: false,
      );

      // Act
      final json = {
        'id': originalQuiz.id,
        'name': originalQuiz.name,
        'grade': originalQuiz.grade,
        'type': originalQuiz.type,
        'numberOfAttempts': originalQuiz.numberOfAttempts,
        'timeLimit': originalQuiz.timeLimit,
        'subject': {'id': 1, 'name': 'Math', 'code': 'MATH101'},
        'lesson': {'id': 1, 'name': 'Algebra'},
        'content': [],
        'isRecord': originalQuiz.isRecord,
      };

      final deserializedQuiz = Quiz.fromJson(json);

      // Assert
      expect(deserializedQuiz.id, equals(originalQuiz.id));
      expect(deserializedQuiz.timeLimit, equals(originalQuiz.timeLimit));
      expect(deserializedQuiz.isRecord, equals(originalQuiz.isRecord));
    });

    test('should handle missing isRecord field in JSON', () {
      // Arrange
      final json = {
        'id': 1,
        'name': 'Test Quiz',
        'grade': 100,
        'type': 'regular',
        'numberOfAttempts': 3,
        'timeLimit': 30,
        'subject': {'id': 1, 'name': 'Math', 'code': 'MATH101'},
        'lesson': {'id': 1, 'name': 'Algebra'},
        'content': [],
        // isRecord field is missing
      };

      // Act
      final quiz = Quiz.fromJson(json);

      // Assert
      expect(quiz.isRecord, isFalse); // Should default to false
    });
  });

  group('WeekQuiz Model Tests', () {
    test('should create WeekQuiz with optional timeLimit', () {
      // Arrange & Act
      final weekQuiz = WeekQuiz(
        id: 1,
        name: 'Week 1 Quiz',
        timeLimit: null, // Optional timeLimit
        grade: 100,
        numberOfAttempts: 2,
        type: 'regular',
        isRecord: false,
      );

      // Assert
      expect(weekQuiz.timeLimit, isNull);
      expect(weekQuiz.isRecord, isFalse);
      expect(weekQuiz.hasTimer, isFalse);
      expect(weekQuiz.isRecordQuiz, isFalse);
    });

    test('should create record WeekQuiz', () {
      // Arrange & Act
      final weekQuiz = WeekQuiz(
        id: 1,
        name: 'Record Quiz',
        timeLimit: null,
        grade: 50,
        numberOfAttempts: 1,
        type: 'record',
        isRecord: true,
      );

      // Assert
      expect(weekQuiz.isRecord, isTrue);
      expect(weekQuiz.hasTimer, isFalse);
      expect(weekQuiz.isRecordQuiz, isTrue);
    });

    test('should serialize and deserialize WeekQuiz correctly', () {
      // Arrange
      final json = {
        'id': 1,
        'name': 'Test Quiz',
        'timeLimit': 30,
        'grade': 100,
        'numberOfAttempts': 2,
        'type': 'regular',
        'userAttempts': 0,
        'status': 'open',
        'isRecord': false,
      };

      // Act
      final weekQuiz = WeekQuiz.fromJson(json);

      // Assert
      expect(weekQuiz.timeLimit, equals(30));
      expect(weekQuiz.isRecord, isFalse);
      expect(weekQuiz.hasTimer, isTrue);
    });

    test('should handle missing isRecord field in WeekQuiz JSON', () {
      // Arrange
      final json = {
        'id': 1,
        'name': 'Test Quiz',
        'timeLimit': null,
        'grade': 100,
        'numberOfAttempts': 2,
        'type': 'regular',
        'userAttempts': 0,
        'status': 'open',
        // isRecord field is missing
      };

      // Act
      final weekQuiz = WeekQuiz.fromJson(json);

      // Assert
      expect(weekQuiz.isRecord, isFalse); // Should default to false
    });
  });
}
