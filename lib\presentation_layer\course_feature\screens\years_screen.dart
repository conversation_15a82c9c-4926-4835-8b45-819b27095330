import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../data_layer/model/enrolled_semester.dart';
import '../../../data_layer/provider/authentication_provider.dart';
import '../../../data_layer/service/semester_service.dart';
import '../../../translation/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_images.dart';
import '../../../managers/theme_manager.dart';
import '../widgets/year_item.dart';

class YearsScreen extends StatefulWidget {
  const YearsScreen({super.key});

  @override
  State<YearsScreen> createState() => _YearsScreenState();
}

class _YearsScreenState extends State<YearsScreen> {
  late AuthenticationProvider _authenticationProvider;
  List<EnrolledSemester> enrolledSemesters = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _authenticationProvider = context.read<AuthenticationProvider>();
    _fetchEnrolledSemesters();
  }

  Future<void> _fetchEnrolledSemesters() async {
    try {
      final semesterService = SemesterService();
      final semesters = await semesterService.getEnrolledSemesters();
      setState(() {
        enrolledSemesters = semesters;
        isLoading = false;
      });
    } catch (e) {
      print('Error: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  bool isYearOpen(int yearNumber) {
    final int firstSemester = (yearNumber * 2) - 1;
    final int secondSemester = yearNumber * 2;
    
    return enrolledSemesters.any((sem) => sem.semesterNo == firstSemester) || 
           enrolledSemesters.any((sem) => sem.semesterNo == secondSemester);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        centerTitle: true,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        title: Text(LocaleKeys.courses.tr()),
        titleTextStyle:
            ThemeManager.semiBold(size: 20, color: AppColors.warmWhite),
      ),
      body: isLoading 
        ? const Center(child: CircularProgressIndicator())
        : SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 30.0, left: 16, right: 16),
              child: Column(
                children: [
                  YearItem(
                      title: LocaleKeys.firstYear.tr(),
                      image: AppImages.semOne,
                      isOpen: isYearOpen(1),
                      semesterNo: 1,
                      enrolledSemesters: enrolledSemesters),
                  YearItem(
                      title: LocaleKeys.secondYear.tr(),
                      image: AppImages.semTwo,
                      isOpen: isYearOpen(2),
                      semesterNo: 2,
                      enrolledSemesters: enrolledSemesters),
                  YearItem(
                      title: LocaleKeys.thirdYear.tr(),
                      image: AppImages.semThree,
                      isOpen: isYearOpen(3),
                      semesterNo: 3,
                      enrolledSemesters: enrolledSemesters),
                  YearItem(
                      title: LocaleKeys.fourthYear.tr(),
                      image: AppImages.semFour,
                      isOpen: isYearOpen(4),
                      semesterNo: 4,
                      enrolledSemesters: enrolledSemesters),
                  YearItem(
                      title: LocaleKeys.fifthYear.tr(),
                      image: AppImages.semFive,
                      isOpen: isYearOpen(5),
                      semesterNo: 5,
                      enrolledSemesters: enrolledSemesters),
                ],
              ),
            ),
          ),
    );
  }
}
