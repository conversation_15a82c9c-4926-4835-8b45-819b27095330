import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:provider/provider.dart';
import 'package:arepsalin/data_layer/provider/authentication_provider.dart';
import 'package:arepsalin/presentation_layer/home_feature/screens/home_screen.dart';
import 'package:arepsalin/presentation_layer/profile_feature/screens/user_screen.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_images.dart';
import '../../../data_layer/provider/user_provider.dart';

class InitialScreen extends StatefulWidget {
  const InitialScreen({super.key});

  @override
  State<InitialScreen> createState() => _InitialScreenState();
}

class _InitialScreenState extends State<InitialScreen> {
  late AuthenticationProvider _authenticationProvider;

  Future<void> getUserData() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.fetchUser();
    //
    // if (uid != null) {
    //   DocumentSnapshot userDoc =
    //       await FirebaseFirestore.instance.collection('users').doc(uid).get();
    //
    //   if (userDoc.exists) {
    //     var userData = userDoc.data() as Map<String, dynamic>;
    //
    //     UserModel user = UserModel.fromJson(userData);
    //     _authenticationProvider.updateUser(user);
    //   }
    // }
  }

  List<Widget> _buildScreens() {
    return [
      const HomeScreen(),
      const UserScreen(),
    ];
  }

  List<PersistentBottomNavBarItem> _navBarsItems() {
    return [
      PersistentBottomNavBarItem(
        icon: ImageIcon(AssetImage(AppImages.bottomHome)),
        activeColorPrimary: AppColors.primary,
        inactiveColorPrimary: AppColors.borderGrey,
      ),
      PersistentBottomNavBarItem(
        icon: ImageIcon(AssetImage(AppImages.bottomPerson)),
        activeColorPrimary: AppColors.primary,
        inactiveColorPrimary: AppColors.borderGrey,
      ),
    ];
  }

  @override
  void initState() {
    super.initState();
    _authenticationProvider = context.read<AuthenticationProvider>();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await getUserData();
    });
  }

  final PersistentTabController _controller =
      PersistentTabController(initialIndex: 0);

  @override
  Widget build(BuildContext context) {
    return PersistentTabView(
      backgroundColor: AppColors.white,
      bottomScreenMargin: 20,
      neumorphicProperties: const NeumorphicProperties(
          border: Border.symmetric(vertical: BorderSide(color: Colors.black))),
      context,
      controller: _controller,
      screens: _buildScreens(),
      items: _navBarsItems(),
      confineToSafeArea: true,
      handleAndroidBackButtonPress: true,
      resizeToAvoidBottomInset: true,
      stateManagement: true,
      hideNavigationBarWhenKeyboardAppears: true,
      decoration: NavBarDecoration(
          colorBehindNavBar: Colors.transparent,
          border: Border.symmetric(
              horizontal: BorderSide(
                  color: AppColors.black.withOpacity(0.3), width: 1))),
      navBarStyle: NavBarStyle.style6,
    );
  }
}
