import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:no_screenshot/no_screenshot.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:arepsalin/constants/app_colors.dart';
import 'package:arepsalin/managers/theme_manager.dart';

import '../../../constants/app_images.dart';

class SingleVideoScreen extends StatefulWidget {
  const SingleVideoScreen({
    Key? key, 
    required this.title,
    required this.videoUrl
  }) : super(key: key);

  final String title;
  final String videoUrl;
  
  @override
  State<SingleVideoScreen> createState() => _SingleVideoScreenState();
}

class _SingleVideoScreenState extends State<SingleVideoScreen> {
  late YoutubePlayerController _controller;
  final _noScreenshot = NoScreenshot.instance;


  Future<void> _enableScreenshotProtection() async {
    try {
      await _noScreenshot.screenshotOff();
      debugPrint('Screenshot protection enabled');
    } catch (e) {
      debugPrint('Error enabling screenshot protection: $e');
    }
  }

  Future<void> _disableScreenshotProtection() async {
    try {
      await _noScreenshot.screenshotOn();
      debugPrint('Screenshot protection disabled');
    } catch (e) {
      debugPrint('Error disabling screenshot protection: $e');
    }
  }
  @override
  void initState() {
    super.initState();
    _enableScreenshotProtection();
    final videoId = YoutubePlayer.convertUrlToId(widget.videoUrl);
    _controller = YoutubePlayerController(
      initialVideoId: videoId ?? '',
      flags: const YoutubePlayerFlags(
        autoPlay: false,
        mute: false,
        enableCaption: true,
      ),
    );
  }

  @override
  void dispose() {
    _disableScreenshotProtection();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        centerTitle: true,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        title: Text(widget.title),
        titleTextStyle: ThemeManager.semiBold(
          size: 20, 
          color: AppColors.warmWhite
        ),
      ),
      body: Stack(
        children: [
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Image(
              image: AssetImage(AppImages.videoBack),
              fit: BoxFit.fill,
            )
          ),
          Container(
            decoration: BoxDecoration(
              color: AppColors.white.withOpacity(0.3)
            ),
            width: double.infinity,
            child: Column(
              children: [
                YoutubePlayer(
                  controller: _controller,
                  showVideoProgressIndicator: true,
                  progressColors: const ProgressBarColors(
                    playedColor: AppColors.primary,
                    handleColor: AppColors.primary,
                  ),
                  onReady: () {
                    _controller.addListener(() {});
                  },
                ),
                const SizedBox(height: 80),
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 5),
                  margin: const EdgeInsets.symmetric(
                      vertical: 20, horizontal: 10),
                  decoration: BoxDecoration(
                      color: AppColors.white.withOpacity(0.2),
                      border: Border.all(color: AppColors.grey),
                      borderRadius: BorderRadius.circular(10)),
                  child: ListView(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    children: [
                      Column(
                        children: [
                          Text(
                            '«أَ رَحْ فْضَ ٱلْةِ. شَتَايَ تُبِّ انِ. هَذَا أُارِ حَيَاتِي. بِٱسْرْ َ.ً» (مزامير 3:63-4)',
                            maxLines: 4,
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.visible,
                            softWrap: true,
                            style: ThemeManager.semiBold(
                                size: 22, color: AppColors.black),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            )
          ),
        ],
      ),
    );
  }
}
