import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../../managers/secure_storage.dart';
import '../model/quiz_model.dart';
import '../model/week_quiz_model.dart';

class QuizService {
  final String baseUrl = 'https://aripsalin-api-production.up.railway.app';
  final storage = SecureStorage();

  Future<List<WeekQuiz>> getLessonQuizzes(int lessonId) async {
    try {
      final token = await storage.getToken("access_token");

      final response = await http.get(
        Uri.parse('$baseUrl/quiz/lesson/$lessonId/simple'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        final List<dynamic> quizzesData = responseData['data'] as List<dynamic>;
        return quizzesData.map((json) => WeekQuiz.fromJson(json)).toList();
      }

      throw Exception('Failed to fetch lesson quizzes: ${response.statusCode}');
    } catch (e) {
      debugPrint('Error fetching lesson quizzes: $e');
      return [];
    }
  }

  Future<Quiz?> getQuizQuestions(int quizId) async {
    try {
      final token = await storage.getToken("access_token");

      final response = await http.get(
        Uri.parse('$baseUrl/quiz/$quizId/random'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        final Map<String, dynamic> quizData =
            responseData['data'] as Map<String, dynamic>;
        print(responseData);
        return Quiz.fromJson(quizData);
      }

      throw Exception('Failed to fetch quiz questions: ${response.statusCode}');
    } catch (e) {
      debugPrint('Error fetching quiz questions: $e');
      return null;
    }
  }

  Future<String?> uploadAudioFile(File audioFile) async {
    try {
      final token = await storage.getToken("access_token");

      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/upload/audio'),
      );

      request.headers['Authorization'] = 'Bearer $token';

      request.files.add(
        await http.MultipartFile.fromPath(
          'audio',
          audioFile.path,
        ),
      );

      var response = await request.send();

      if (response.statusCode == 200 || response.statusCode == 201) {
        var responseData = await response.stream.bytesToString();
        var jsonData = jsonDecode(responseData);
        return jsonData['fileUrl'] ?? jsonData['url'];
      }

      debugPrint('Error uploading audio file: ${response.statusCode}');
      return null;
    } catch (e) {
      debugPrint('Error uploading audio file: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> submitQuizAttempt(
      int quizId, int timeTaken, List<Map<String, dynamic>> answers, bool isRedo) async {
    try {
      final token = await storage.getToken("access_token");
      final body = isRedo==true?  jsonEncode({
          'timeTaken':timeTaken,
          'quizId': quizId,
          'answers': answers,
           'redo': true
        }):jsonEncode({
          'timeTaken':timeTaken,
          'quizId': quizId,
          'answers': answers,
        });
      final response = await http.post(
        Uri.parse('$baseUrl/quiz-answers'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: body,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return responseData['data'];
      }

      debugPrint('Error submitting quiz attempt: ${response.statusCode}');
      return null;
    } catch (e) {
      debugPrint('Error submitting quiz attempt: $e');
      return null;
    }
  }

  Future<int> getQuizAttempts(int quizId) async {
    try {
      final token = await storage.getToken("access_token");

      final response = await http.get(
        Uri.parse('$baseUrl/quiz/$quizId/attempts'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return responseData['count'] ?? 0;
      }

      return 0;
    } catch (e) {
      debugPrint('Error fetching quiz attempts: $e');
      return 0;
    }
  }
}
