import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:arepsalin/data_layer/model/exam_model.dart';

class QuizProvider extends ChangeNotifier {
  ExamModel? exam;
  int currentQuestionIndex = 0;
  Map<int, String> chosenAnswers = {};

  late int remainingMinutes;
  late int remainingSeconds;
  late Duration _examDuration;
  late Timer _timer;

  void setExam(ExamModel newExam) {
    exam = newExam;
    currentQuestionIndex = 0;
    chosenAnswers.clear();
    _examDuration = Duration(minutes: newExam.duration);
    remainingMinutes = newExam.duration;
    remainingSeconds = 0;
    startCountdownTimer();
  }

  void startCountdownTimer() {
    Duration duration = _examDuration;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_examDuration.inSeconds > 0) {
        duration -= const Duration(seconds: 1);
        remainingMinutes = duration.inMinutes;
        remainingSeconds = duration.inSeconds % 60;
        notifyListeners();
      } else {
        // Quiz time is up
        _timer.cancel();
        // You can perform any action here when the time is up
      }
    });
  }

  void goToNextQuestion(String chosenAnswer) {
    chosenAnswers[currentQuestionIndex] = chosenAnswer;
    if (currentQuestionIndex < exam!.questions.length - 1) {
      currentQuestionIndex++;
    }
    notifyListeners();
  }

  bool isQuizFinished() {
    return currentQuestionIndex == exam!.questions.length - 1;
  }

  void updateChosenAnswer(String chosenAnswer) {
    chosenAnswers[currentQuestionIndex] = chosenAnswer;
    notifyListeners();
  }

  void disposeTime() {
    _timer.cancel();
  }

  void goToPreviousQuestion() {
    if (currentQuestionIndex > 0) {
      currentQuestionIndex--;
      notifyListeners();
    }
  }
}
